Warning	1	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	2	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	3	The primary reference "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	4	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	5	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	6	The primary reference "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	7	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	8	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	9	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	10	Could not resolve this reference. Could not locate the assembly "TouchlessLib". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.	Sales and Inventory System
Warning	11	Cannot get the file path for type library "237f4bec-8ae5-41e1-ae84-b194e4670597" version 13.0. Library not registered. (Exception from HRESULT: 0x8002801D (TYPE_E_LIBNOTREGISTERED))	Sales and Inventory System
Error	12	The definition of this report is not valid or supported by this version of Reporting Services. The report definition may have been created with a later version of Reporting Services, or contain content that is not well-formed or not valid based on Reporting Services schemas. Details: Deserialization failed: The element 'Report' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' has invalid child element 'ReportParametersLayout' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition'. List of possible elements expected: 'Description, Author, AutoRefresh, DataSources, DataSets, Body, ReportParameters, Code, Width, Page, EmbeddedImages, Language, CodeModules, Classes, CustomProperties, Variables, DeferVariableEvaluation, ConsumeContainerWhitespace, DataTransform, DataSchema, DataElementName, DataElementStyle' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' as well as any element in namespace '##other'. Line 343, position 4.	D:\b Sales and Inventory System\Reports\InvoiceReport.rdlc	Sales and Inventory System
Error	13	The definition of this report is not valid or supported by this version of Reporting Services. The report definition may have been created with a later version of Reporting Services, or contain content that is not well-formed or not valid based on Reporting Services schemas. Details: Deserialization failed: The element 'Report' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' has invalid child element 'ReportParametersLayout' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition'. List of possible elements expected: 'Description, Author, AutoRefresh, DataSources, DataSets, Body, ReportParameters, Code, Width, Page, EmbeddedImages, Language, CodeModules, Classes, CustomProperties, Variables, DeferVariableEvaluation, ConsumeContainerWhitespace, DataTransform, DataSchema, DataElementName, DataElementStyle' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' as well as any element in namespace '##other'. Line 292, position 4.	D:\b Sales and Inventory System\Reports\SalesReport.rdlc	Sales and Inventory System
Error	14	The definition of this report is not valid or supported by this version of Reporting Services. The report definition may have been created with a later version of Reporting Services, or contain content that is not well-formed or not valid based on Reporting Services schemas. Details: Deserialization failed: The element 'Report' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' has invalid child element 'ReportParametersLayout' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition'. List of possible elements expected: 'Description, Author, AutoRefresh, DataSources, DataSets, Body, ReportParameters, Code, Width, Page, EmbeddedImages, Language, CodeModules, Classes, CustomProperties, Variables, DeferVariableEvaluation, ConsumeContainerWhitespace, DataTransform, DataSchema, DataElementName, DataElementStyle' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' as well as any element in namespace '##other'. Line 321, position 4.	D:\b Sales and Inventory System\Reports\StockInReport.rdlc	Sales and Inventory System
Error	15	The definition of this report is not valid or supported by this version of Reporting Services. The report definition may have been created with a later version of Reporting Services, or contain content that is not well-formed or not valid based on Reporting Services schemas. Details: Deserialization failed: The element 'Report' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' has invalid child element 'ReportParametersLayout' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition'. List of possible elements expected: 'Description, Author, AutoRefresh, DataSources, DataSets, Body, ReportParameters, Code, Width, Page, EmbeddedImages, Language, CodeModules, Classes, CustomProperties, Variables, DeferVariableEvaluation, ConsumeContainerWhitespace, DataTransform, DataSchema, DataElementName, DataElementStyle' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' as well as any element in namespace '##other'. Line 377, position 4.	D:\b Sales and Inventory System\Reports\CustomerReport.rdlc	Sales and Inventory System
Error	16	The definition of this report is not valid or supported by this version of Reporting Services. The report definition may have been created with a later version of Reporting Services, or contain content that is not well-formed or not valid based on Reporting Services schemas. Details: Deserialization failed: The element 'Report' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' has invalid child element 'ReportParametersLayout' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition'. List of possible elements expected: 'Description, Author, AutoRefresh, DataSources, DataSets, Body, ReportParameters, Code, Width, Page, EmbeddedImages, Language, CodeModules, Classes, CustomProperties, Variables, DeferVariableEvaluation, ConsumeContainerWhitespace, DataTransform, DataSchema, DataElementName, DataElementStyle' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' as well as any element in namespace '##other'. Line 435, position 4.	D:\b Sales and Inventory System\Reports\SupplierReport.rdlc	Sales and Inventory System
Error	17	The definition of this report is not valid or supported by this version of Reporting Services. The report definition may have been created with a later version of Reporting Services, or contain content that is not well-formed or not valid based on Reporting Services schemas. Details: Deserialization failed: The element 'Report' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' has invalid child element 'ReportParametersLayout' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition'. List of possible elements expected: 'Description, Author, AutoRefresh, DataSources, DataSets, Body, ReportParameters, Code, Width, Page, EmbeddedImages, Language, CodeModules, Classes, CustomProperties, Variables, DeferVariableEvaluation, ConsumeContainerWhitespace, DataTransform, DataSchema, DataElementName, DataElementStyle' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' as well as any element in namespace '##other'. Line 494, position 4.	D:\b Sales and Inventory System\Reports\CustomerLedgerReport.rdlc	Sales and Inventory System
Error	18	The definition of this report is not valid or supported by this version of Reporting Services. The report definition may have been created with a later version of Reporting Services, or contain content that is not well-formed or not valid based on Reporting Services schemas. Details: Deserialization failed: The element 'Report' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' has invalid child element 'ReportParametersLayout' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition'. List of possible elements expected: 'Description, Author, AutoRefresh, DataSources, DataSets, Body, ReportParameters, Code, Width, Page, EmbeddedImages, Language, CodeModules, Classes, CustomProperties, Variables, DeferVariableEvaluation, ConsumeContainerWhitespace, DataTransform, DataSchema, DataElementName, DataElementStyle' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' as well as any element in namespace '##other'. Line 494, position 4.	D:\b Sales and Inventory System\Reports\SupplierLedgerReport.rdlc	Sales and Inventory System
Error	19	The definition of this report is not valid or supported by this version of Reporting Services. The report definition may have been created with a later version of Reporting Services, or contain content that is not well-formed or not valid based on Reporting Services schemas. Details: Deserialization failed: The element 'Report' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' has invalid child element 'ReportParametersLayout' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition'. List of possible elements expected: 'Description, Author, AutoRefresh, DataSources, DataSets, Body, ReportParameters, Code, Width, Page, EmbeddedImages, Language, CodeModules, Classes, CustomProperties, Variables, DeferVariableEvaluation, ConsumeContainerWhitespace, DataTransform, DataSchema, DataElementName, DataElementStyle' in namespace 'http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition' as well as any element in namespace '##other'. Line 425, position 4.	D:\b Sales and Inventory System\Reports\SalesTaxReport.rdlc	Sales and Inventory System
Warning	20	The referenced component 'TouchlessLib' could not be found. 	Sales and Inventory System
Warning	21	The referenced component 'PAGEOBJECTMODELLib' could not be found. 	Sales and Inventory System
Warning	22	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptBarcodeLabelPrinting.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	23	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditors.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	24	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTerms.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	25	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTermsStatements.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	26	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTermsStatementsByCustomer.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	27	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCustomerLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	28	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptDebtors.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	29	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptExpenses.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	30	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptGeneralDayBook.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	31	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptGeneralLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	32	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptInvoice.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	33	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptInvoice1.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	34	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptInvoice2.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	35	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptInvoiceReturn.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	36	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptOverall.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	37	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptPurchase.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	38	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptPurchaseDayBook.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	39	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptQuotation.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	40	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptQuotation1.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	41	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	42	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales1.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	43	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales2.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	44	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesmanCommission.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	45	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesmanLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	46	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesmanLedger_2.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	47	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesTaxReport.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	48	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptService.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	49	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptServiceReceipt.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	50	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptServiceTaxReport.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	51	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptStockIn.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	52	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptStockOut.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	53	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSupplierLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	54	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptTrialBalance.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	55	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptVoucher.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	56	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTexpenses.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	57	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTpurchases.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	58	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTsales.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	59	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTservice.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Error	60	Unable to open module file 'D:\b Sales and Inventory System\My Project\AssemblyInfo.vb': System Error &*********&	D:\b Sales and Inventory System\My Project\AssemblyInfo.vb	1	1	Sales and Inventory System
Error	61	Unable to open module file 'D:\b Sales and Inventory System\SubRPTexpenses.vb': System Error &*********&	D:\b Sales and Inventory System\SubRPTexpenses.vb	1	1	Sales and Inventory System
Error	62	Unable to open module file 'D:\b Sales and Inventory System\SubRPTpurchases.vb': System Error &*********&	D:\b Sales and Inventory System\SubRPTpurchases.vb	1	1	Sales and Inventory System
Error	63	Unable to open module file 'D:\b Sales and Inventory System\SubRPTsales.vb': System Error &*********&	D:\b Sales and Inventory System\SubRPTsales.vb	1	1	Sales and Inventory System
Error	64	Unable to open module file 'D:\b Sales and Inventory System\SubRPTservice.vb': System Error &*********&	D:\b Sales and Inventory System\SubRPTservice.vb	1	1	Sales and Inventory System
Error	65	'Picsart_23_03_19_11_27_15_052' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmAbout.Designer.vb	73	35	Sales and Inventory System
Error	66	'Activate' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmActivation.Designer.vb	159	28	Sales and Inventory System
Error	67	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmChangePassword.Designer.vb	186	30	Sales and Inventory System
Error	68	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmChangePassword.Designer.vb	203	32	Sales and Inventory System
Error	69	'Company1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmCompany.Designer.vb	140	32	Sales and Inventory System
Error	70	'Company1' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmCompany.vb	14	29	Sales and Inventory System
Error	71	'photo' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmCustomer.Designer.vb	247	28	Sales and Inventory System
Error	72	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmCustomer.vb	26	25	Sales and Inventory System
Error	73	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmCustomer.vb	380	25	Sales and Inventory System
Error	74	Argument not specified for parameter 'dateFrom' of 'Public Shared Sub ShowDebtorsReport(dateFrom As Date, dateTo As Date)'.	D:\b Sales and Inventory System\frmDebtorsReport.vb	35	13	Sales and Inventory System
Error	75	Argument not specified for parameter 'dateTo' of 'Public Shared Sub ShowDebtorsReport(dateFrom As Date, dateTo As Date)'.	D:\b Sales and Inventory System\frmDebtorsReport.vb	35	13	Sales and Inventory System
Error	76	Argument not specified for parameter 'dateFrom' of 'Public Shared Sub ShowDebtorsReport(dateFrom As Date, dateTo As Date)'.	D:\b Sales and Inventory System\frmDebtorsReport.vb	54	13	Sales and Inventory System
Error	77	Argument not specified for parameter 'dateTo' of 'Public Shared Sub ShowDebtorsReport(dateFrom As Date, dateTo As Date)'.	D:\b Sales and Inventory System\frmDebtorsReport.vb	54	13	Sales and Inventory System
Error	78	'Picsart_23_03_19_11_27_15_0522' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	152	35	Sales and Inventory System
Error	79	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	203	32	Sales and Inventory System
Error	80	'panelControl1_ContentImage' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	241	30	Sales and Inventory System
Error	81	'Reset2_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	107	29	Sales and Inventory System
Error	82	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	217	29	Sales and Inventory System
Error	83	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	240	37	Sales and Inventory System
Error	84	'Billing_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	173	45	Sales and Inventory System
Error	85	'basket_full_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	220	45	Sales and Inventory System
Error	86	'payment_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	229	39	Sales and Inventory System
Error	87	'edit_file_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	247	45	Sales and Inventory System
Error	88	'Stocks_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	256	45	Sales and Inventory System
Error	89	'User_Group_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	265	46	Sales and Inventory System
Error	90	'Users_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	274	47	Sales and Inventory System
Error	91	'Admin_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	283	46	Sales and Inventory System
Error	92	'Utilities_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	293	45	Sales and Inventory System
Error	93	'Inventory_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	314	47	Sales and Inventory System
Error	94	'messages_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	350	45	Sales and Inventory System
Error	95	'product_sales_report_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	360	44	Sales and Inventory System
Error	96	'report_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	430	45	Sales and Inventory System
Error	97	'Database_Active_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	561	46	Sales and Inventory System
Error	98	'log_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	582	42	Sales and Inventory System
Error	99	'Actions_user_group_new_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	591	50	Sales and Inventory System
Error	100	'Log_Out_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	609	39	Sales and Inventory System
Error	101	'basket_full_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	673	46	Sales and Inventory System
Error	102	'Entypo_d83d_0__512' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	681	49	Sales and Inventory System
Error	103	'Excel_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	865	35	Sales and Inventory System
Error	104	'Reset2_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	881	29	Sales and Inventory System
Error	105	'ShowProfitAndLossReport' is not a member of 'Sales_and_Inventory_System.ReportManager'.	D:\b Sales and Inventory System\frmOverallReport.vb	135	13	Sales and Inventory System
Error	106	'keyboard_icon__1_' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1604	32	Sales and Inventory System
Error	107	'User_Interface_Restore_Window_icon__1_' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1622	32	Sales and Inventory System
Error	108	'Programming_Minimize_Window_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1640	32	Sales and Inventory System
Error	109	'Button_Delete_icon11' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1657	29	Sales and Inventory System
Error	110	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmPOS.vb	531	44	Sales and Inventory System
Error	111	'Maximise_32X32' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmPOS.vb	1127	33	Sales and Inventory System
Error	112	'User_Interface_Restore_Window_icon__1_' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmPOS.vb	1130	33	Sales and Inventory System
Error	113	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment.Designer.vb	167	29	Sales and Inventory System
Error	114	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment_2.Designer.vb	283	29	Sales and Inventory System
Error	115	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment_3.Designer.vb	277	29	Sales and Inventory System
Error	116	'_12' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmProduct.Designer.vb	376	28	Sales and Inventory System
Error	117	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPurchaseReturn.Designer.vb	675	29	Sales and Inventory System
Error	118	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmQuotation.vb	455	44	Sales and Inventory System
Error	119	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmRecoveryPassword.Designer.vb	111	30	Sales and Inventory System
Error	120	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmRecoveryPassword.Designer.vb	129	32	Sales and Inventory System
Error	121	'product_sales_report_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSalesReturn.Designer.vb	1048	29	Sales and Inventory System
Error	122	Type 'rptInvoiceReturn' is not defined.	D:\b Sales and Inventory System\frmSalesReturn.vb	89	28	Sales and Inventory System
Error	123	Argument not specified for parameter 'customerType' of 'Public Shared Sub ShowInvoiceReport(invoiceNo As String, customerType As String)'.	D:\b Sales and Inventory System\frmSalesReturn.vb	111	13	Sales and Inventory System
Error	124	'photo' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSalesman.Designer.vb	202	28	Sales and Inventory System
Error	125	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmSalesman.vb	23	25	Sales and Inventory System
Error	126	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmSalesman.vb	304	25	Sales and Inventory System
Error	127	Type 'rptInvoice1' is not defined.	D:\b Sales and Inventory System\frmServiceBilling.vb	163	28	Sales and Inventory System
Error	128	'CrystalReportViewer1' is not a member of 'Sales_and_Inventory_System.frmReport'.	D:\b Sales and Inventory System\frmServiceBilling.vb	187	13	Sales and Inventory System
Error	129	'Database_Active_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSqlServerSetting.Designer.vb	88	38	Sales and Inventory System
Error	130	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSupplier.Designer.vb	121	29	Sales and Inventory System
Error	131	'ShowProfitAndLossReport' is not a member of 'Sales_and_Inventory_System.ReportManager'.	D:\b Sales and Inventory System\frmTrialBalance.vb	57	13	Sales and Inventory System
Error	132	Unable to open module file 'D:\b Sales and Inventory System\rptBarcodeLabelPrinting.vb': System Error &*********&	D:\b Sales and Inventory System\rptBarcodeLabelPrinting.vb	1	1	Sales and Inventory System
Error	133	Unable to open module file 'D:\b Sales and Inventory System\rptCreditTerms.vb': System Error &*********&	D:\b Sales and Inventory System\rptCreditTerms.vb	1	1	Sales and Inventory System
Error	134	Unable to open module file 'D:\b Sales and Inventory System\rptCreditTermsStatements.vb': System Error &*********&	D:\b Sales and Inventory System\rptCreditTermsStatements.vb	1	1	Sales and Inventory System
Error	135	Unable to open module file 'D:\b Sales and Inventory System\rptCreditTermsStatementsByCustomer.vb': System Error &*********&	D:\b Sales and Inventory System\rptCreditTermsStatementsByCustomer.vb	1	1	Sales and Inventory System
Error	136	Unable to open module file 'D:\b Sales and Inventory System\rptCustomerLedger.vb': System Error &*********&	D:\b Sales and Inventory System\rptCustomerLedger.vb	1	1	Sales and Inventory System
Error	137	Unable to open module file 'D:\b Sales and Inventory System\rptDebtors.vb': System Error &*********&	D:\b Sales and Inventory System\rptDebtors.vb	1	1	Sales and Inventory System
Error	138	Unable to open module file 'D:\b Sales and Inventory System\rptExpenses.vb': System Error &*********&	D:\b Sales and Inventory System\rptExpenses.vb	1	1	Sales and Inventory System
Error	139	Unable to open module file 'D:\b Sales and Inventory System\rptGeneralDayBook.vb': System Error &*********&	D:\b Sales and Inventory System\rptGeneralDayBook.vb	1	1	Sales and Inventory System
Error	140	Unable to open module file 'D:\b Sales and Inventory System\rptGeneralLedger.vb': System Error &*********&	D:\b Sales and Inventory System\rptGeneralLedger.vb	1	1	Sales and Inventory System
Error	141	Unable to open module file 'D:\b Sales and Inventory System\rptInvoice.vb': System Error &*********&	D:\b Sales and Inventory System\rptInvoice.vb	1	1	Sales and Inventory System
Error	142	Unable to open module file 'D:\b Sales and Inventory System\rptInvoice1.vb': System Error &*********&	D:\b Sales and Inventory System\rptInvoice1.vb	1	1	Sales and Inventory System
Error	143	Unable to open module file 'D:\b Sales and Inventory System\rptInvoice2.vb': System Error &*********&	D:\b Sales and Inventory System\rptInvoice2.vb	1	1	Sales and Inventory System
Error	144	Unable to open module file 'D:\b Sales and Inventory System\rptInvoiceReturn.vb': System Error &*********&	D:\b Sales and Inventory System\rptInvoiceReturn.vb	1	1	Sales and Inventory System
Error	145	Unable to open module file 'D:\b Sales and Inventory System\rptOverall.vb': System Error &*********&	D:\b Sales and Inventory System\rptOverall.vb	1	1	Sales and Inventory System
Error	146	Unable to open module file 'D:\b Sales and Inventory System\rptPurchase.vb': System Error &*********&	D:\b Sales and Inventory System\rptPurchase.vb	1	1	Sales and Inventory System
Error	147	Unable to open module file 'D:\b Sales and Inventory System\rptPurchaseDayBook.vb': System Error &*********&	D:\b Sales and Inventory System\rptPurchaseDayBook.vb	1	1	Sales and Inventory System
Error	148	Unable to open module file 'D:\b Sales and Inventory System\rptQuotation.vb': System Error &*********&	D:\b Sales and Inventory System\rptQuotation.vb	1	1	Sales and Inventory System
Error	149	Unable to open module file 'D:\b Sales and Inventory System\rptQuotation1.vb': System Error &*********&	D:\b Sales and Inventory System\rptQuotation1.vb	1	1	Sales and Inventory System
Error	150	Unable to open module file 'D:\b Sales and Inventory System\rptSalesTaxReport.vb': System Error &*********&	D:\b Sales and Inventory System\rptSalesTaxReport.vb	1	1	Sales and Inventory System
Error	151	Unable to open module file 'D:\b Sales and Inventory System\rptSalesmanCommission.vb': System Error &*********&	D:\b Sales and Inventory System\rptSalesmanCommission.vb	1	1	Sales and Inventory System
Error	152	Unable to open module file 'D:\b Sales and Inventory System\rptSalesmanLedger.vb': System Error &*********&	D:\b Sales and Inventory System\rptSalesmanLedger.vb	1	1	Sales and Inventory System
Error	153	Unable to open module file 'D:\b Sales and Inventory System\rptSalesmanLedger_2.vb': System Error &*********&	D:\b Sales and Inventory System\rptSalesmanLedger_2.vb	1	1	Sales and Inventory System
Error	154	Unable to open module file 'D:\b Sales and Inventory System\rptService.vb': System Error &*********&	D:\b Sales and Inventory System\rptService.vb	1	1	Sales and Inventory System
Error	155	Unable to open module file 'D:\b Sales and Inventory System\rptServiceReceipt.vb': System Error &*********&	D:\b Sales and Inventory System\rptServiceReceipt.vb	1	1	Sales and Inventory System
Error	156	Unable to open module file 'D:\b Sales and Inventory System\rptServiceTaxReport.vb': System Error &*********&	D:\b Sales and Inventory System\rptServiceTaxReport.vb	1	1	Sales and Inventory System
Error	157	Unable to open module file 'D:\b Sales and Inventory System\rptStockIn.vb': System Error &*********&	D:\b Sales and Inventory System\rptStockIn.vb	1	1	Sales and Inventory System
Error	158	Unable to open module file 'D:\b Sales and Inventory System\rptStockOut.vb': System Error &*********&	D:\b Sales and Inventory System\rptStockOut.vb	1	1	Sales and Inventory System
Error	159	Unable to open module file 'D:\b Sales and Inventory System\rptSupplierLedger.vb': System Error &*********&	D:\b Sales and Inventory System\rptSupplierLedger.vb	1	1	Sales and Inventory System
Error	160	Unable to open module file 'D:\b Sales and Inventory System\rptVoucher.vb': System Error &*********&	D:\b Sales and Inventory System\rptVoucher.vb	1	1	Sales and Inventory System
Error	161	Maximum number of errors has been exceeded.	Sales and Inventory System
