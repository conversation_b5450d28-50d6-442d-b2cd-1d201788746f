<?xml version="1.0"?>
<configuration>
	<configSections>
		<sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="Sales_and_Inventory_System.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
		</sectionGroup>
	</configSections>
	<connectionStrings>
		<add name="Sales_and_Inventory_System.My.MySettings.SIS_DBConnectionString1" connectionString="Data Source=.\;Initial Catalog=INV_DB;Integrated Security=True;MultipleActiveResultSets=True" providerName="System.Data.SqlClient"/>
	</connectionStrings>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/>
	</startup>
	<userSettings>
		<Sales_and_Inventory_System.My.MySettings>
			<setting name="checked" serializeAs="String">
				<value>False</value>
			</setting>
			<setting name="day" serializeAs="String">
				<value/>
			</setting>
			<setting name="month" serializeAs="String">
				<value/>
			</setting>
			<setting name="year" serializeAs="String">
				<value/>
			</setting>
			<setting name="setting" serializeAs="String">
				<value/>
			</setting>
		</Sales_and_Inventory_System.My.MySettings>
	</userSettings>
</configuration>
