﻿Public Class frmCamera
    ' TouchlessLib معطل - مكتبة الكاميرا غير متوفرة
    ' Public CamMgr As TouchlessLib.TouchlessMgr
    Private Sub WebcamImage_FormClosing(sender As Object, e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        Try
            Timer1.Enabled = False
            ' TouchlessLib معطل
            ' CamMgr.CurrentCamera.Dispose()
            ' CamMgr.Cameras.Item(cmbCamera.SelectedIndex).Dispose()
            ' CamMgr.Dispose()
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Form1_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        ' TouchlessLib معطل - مكتبة الكاميرا غير متوفرة
        ' CamMgr = New TouchlessLib.TouchlessMgr
        TempFileNames2 = ""

        ' تعطيل وظائف الكاميرا
        MessageBox.Show("وظيفة الكاميرا معطلة حالياً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Me.Close()

        ' For i As Integer = 0 To CamMgr.Cameras.Count - 1
        '     cmbCamera.Items.Add(CamMgr.Cameras(i).ToString)
        ' Next
        ' If cmbCamera.Items.Count > 0 Then
        '     cmbCamera.SelectedIndex = 0
        '     Timer1.Enabled = True
        ' Else
        '     MsgBox("There are no Camera ...")
        '     Me.Close()
        ' End If

    End Sub



    Private Sub cmbCamera_SelectedIndexChanged(sender As System.Object, e As System.EventArgs) Handles cmbCamera.SelectedIndexChanged
        ' TouchlessLib معطل
        ' CamMgr.CurrentCamera = CamMgr.Cameras.ElementAt(cmbCamera.SelectedIndex)
    End Sub

    Private Sub Timer1_Tick(sender As System.Object, e As System.EventArgs) Handles Timer1.Tick
        ' TouchlessLib معطل
        ' picFeed.Image = CamMgr.CurrentCamera.GetCurrentImage()
    End Sub

    Private Sub btnCapture_Click(sender As System.Object, e As System.EventArgs) Handles btnCapture.Click
        ' TouchlessLib معطل
        ' picPreview.Image = CamMgr.CurrentCamera.GetCurrentImage()
        ' btnSave.Enabled = True
        MessageBox.Show("وظيفة الكاميرا معطلة حالياً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub btnSave_Click(sender As System.Object, e As System.EventArgs) Handles btnSave.Click
        ' TouchlessLib معطل
        ' Dim sTempFileName As String = System.IO.Path.GetTempFileName()
        ' TempFileNames2 = sTempFileName
        ' Dim b As Bitmap = picPreview.Image
        ' b.Save(sTempFileName, System.Drawing.Imaging.ImageFormat.Jpeg)
        ' Timer1.Enabled = False
        ' CamMgr.CurrentCamera.Dispose()
        ' CamMgr.Cameras.Item(cmbCamera.SelectedIndex).Dispose()
        ' CamMgr.Dispose()
        MessageBox.Show("وظيفة الكاميرا معطلة حالياً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Me.Close()
    End Sub


End Class
