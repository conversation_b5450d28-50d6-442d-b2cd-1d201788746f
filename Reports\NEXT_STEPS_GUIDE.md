# دليل إكمال تحويل التقارير المتبقية

## الحالة الحالية
تم تحويل **6 من 14 تقرير** بنجاح (43% مكتمل)

### التقارير المحولة ✅:
1. تقرير الفواتير
2. تقرير المبيعات  
3. تقرير المدينين والدائنين
4. تقرير المشتريات
5. تقرير المخزون (متوفر ومنتهي)
6. تقرير الأرباح والخسائر

### التقارير المتبقية ⏳:
1. تقرير العملاء
2. تقرير الموردين
3. تقرير دفتر الأستاذ العام
4. تقرير دفتر اليومية
5. تقرير الضرائب
6. تقرير عمولات المندوبين
7. تقرير دفتر أستاذ العميل
8. تقرير دفتر أستاذ المورد

## خطوات إكمال التحويل

### 1. تقرير العملاء
**الملفات المطلوبة:**
- `Reports\CustomerReport.rdlc`
- تحديث `frmCustomerRecord1.vb` (إذا وجد)

**الخطوات:**
```vb
' في ReportManager.vb - الدالة موجودة بالفعل
Public Shared Sub ShowCustomerReport()

' إنشاء RDLC جديد بالحقول:
- CustomerID, Name, Gender, Address, City, State
- ZipCode, ContactNo, EmailID, CustomerType
```

### 2. تقرير الموردين
**الملفات المطلوبة:**
- `Reports\SupplierReport.rdlc`
- تحديث `frmSupplierRecord.vb` (إذا وجد)

**الخطوات:**
```vb
' في ReportManager.vb - الدالة موجودة بالفعل
Public Shared Sub ShowSupplierReport()

' إنشاء RDLC جديد بالحقول:
- SupplierID, SupplierName, Address, City, State
- ZipCode, ContactNo, EmailID
```

### 3. تقرير دفتر الأستاذ العام
**الملفات المطلوبة:**
- `Reports\GeneralLedgerReport.rdlc`
- تحديث `frmGeneralLedger.vb`

**الخطوات:**
```vb
' البحث عن الفورم المسؤول عن دفتر الأستاذ العام
' تحديث الكود ليستخدم:
ReportManager.ShowGeneralLedgerReport(dateFrom, dateTo)

' إنشاء RDLC جديد بالحقول:
- Date, Name, LedgerNo, Label, Debit, Credit, PartyID
```

### 4. تقرير دفتر اليومية
**الملفات المطلوبة:**
- `Reports\GeneralDayBookReport.rdlc`
- تحديث `frmGeneralDayBook.vb`

**الخطوات:**
```vb
' البحث عن الفورم المسؤول عن دفتر اليومية
' تحديث الكود ليستخدم:
ReportManager.ShowGeneralDayBookReport(dateFrom, dateTo)

' إنشاء RDLC جديد بالحقول:
- Date, Name, LedgerNo, Label, Debit, Credit
```

### 5. تقرير الضرائب
**الملفات المطلوبة:**
- `Reports\TaxReport.rdlc`
- تحديث `frmTaxReport.vb`

**الخطوات:**
```vb
' البحث عن فورم تقرير الضرائب
' تحديث الكود ليستخدم:
ReportManager.ShowTaxReport(dateFrom, dateTo)

' إنشاء RDLC جديد بالحقول:
- InvoiceNo, InvoiceDate, CustomerName, VATPer, VAT
```

### 6. تقرير عمولات المندوبين
**الملفات المطلوبة:**
- `Reports\SalesmanCommissionReport.rdlc`
- تحديث `frmSalesmanCommmissionReport.vb`

**الخطوات:**
```vb
' البحث عن فورم تقرير عمولات المندوبين
' تحديث الكود ليستخدم:
ReportManager.ShowSalesmanCommissionReport(dateFrom, dateTo)

' إنشاء RDLC جديد بالحقول:
- SalesmanName, InvoiceNo, InvoiceDate, GrandTotal, CommissionAmount
```

### 7. تقرير دفتر أستاذ العميل
**الملفات المطلوبة:**
- `Reports\CustomerLedgerReport.rdlc`
- تحديث الفورم المسؤول

**الخطوات:**
```vb
' البحث عن فورم دفتر أستاذ العميل
' تحديث الكود ليستخدم:
ReportManager.ShowCustomerLedgerReport(customerID, dateFrom, dateTo)

' إنشاء RDLC جديد بالحقول:
- Date, Name, LedgerNo, Label, Debit, Credit
- Customer details
```

### 8. تقرير دفتر أستاذ المورد
**الملفات المطلوبة:**
- `Reports\SupplierLedgerReport.rdlc`
- تحديث الفورم المسؤول

**الخطوات:**
```vb
' البحث عن فورم دفتر أستاذ المورد
' تحديث الكود ليستخدم:
ReportManager.ShowSupplierLedgerReport(supplierID, dateFrom, dateTo)

' إنشاء RDLC جديد بالحقول:
- Date, Name, LedgerNo, Label, Debit, Credit
- Supplier details
```

## نموذج لإنشاء RDLC جديد

### 1. إنشاء ملف RDLC
```xml
<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition">
  <!-- هيكل التقرير الأساسي -->
</Report>
```

### 2. إضافة دالة تحميل البيانات
```vb
Private Shared Sub LoadYourReportData(parameters..., ds As DataSet)
    Try
        con = New SqlConnection(cs)
        con.Open()
        
        Dim cmd As New SqlCommand("YOUR_SQL_QUERY", con)
        ' إضافة المعاملات
        
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable("YourDataTable")
        adp.Fill(dt)
        ds.Tables.Add(dt)
        
        LoadCompanyDataStatic(ds)
        con.Close()
    Catch ex As Exception
        If con.State = ConnectionState.Open Then con.Close()
        Throw ex
    End Try
End Sub
```

### 3. إضافة دالة عرض التقرير
```vb
Public Shared Sub ShowYourReport(parameters...)
    Try
        Dim ds As New DataSet()
        LoadYourReportData(parameters..., ds)
        
        Dim reportParameters As New List(Of ReportParameter)()
        reportParameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
        
        Dim frmReport As New frmReportViewer()
        frmReport.ShowCustomReport(Application.StartupPath & "\Reports\YourReport.rdlc", ds, reportParameters)
        frmReport.ShowDialog()
        
    Catch ex As Exception
        MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Try
End Sub
```

## أولويات التحويل المقترحة

### المرحلة الأولى (أولوية عالية):
1. **تقرير العملاء** - مهم للعمليات اليومية
2. **تقرير الموردين** - مهم للمشتريات
3. **تقرير الضرائب** - مهم للمحاسبة

### المرحلة الثانية (أولوية متوسطة):
4. **تقرير عمولات المندوبين** - مهم للمبيعات
5. **تقرير دفتر الأستاذ العام** - مهم للمحاسبة

### المرحلة الثالثة (أولوية منخفضة):
6. **تقرير دفتر اليومية** - للمراجعة
7. **تقرير دفتر أستاذ العميل** - للتفاصيل
8. **تقرير دفتر أستاذ المورد** - للتفاصيل

## نصائح مهمة

### 1. قبل البدء:
- انسخ نسخة احتياطية من المشروع
- اختبر التقارير الحالية للتأكد من عملها
- راجع SQL queries في التقارير القديمة

### 2. أثناء التحويل:
- استخدم نفس نمط الكود الموجود
- اختبر كل تقرير بعد تحويله
- تأكد من صحة البيانات المعروضة

### 3. بعد التحويل:
- احذف ملفات Crystal Reports القديمة
- حديث ملف المشروع
- اختبر النظام بالكامل

## الدعم والمراجع

- راجع `Reports\README_ReportViewer_Guide.md` للتفاصيل التقنية
- استخدم التقارير المحولة كمرجع
- اختبر مع بيانات حقيقية قبل النشر

---
**آخر تحديث:** 2025-06-17  
**الحالة:** 43% مكتمل  
**المطور:** Augment Agent
