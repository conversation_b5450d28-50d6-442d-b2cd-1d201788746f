<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:ns1="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="SalesDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <ns1:DataSourceID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</ns1:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="Sales">
      <Query>
        <DataSourceName>SalesDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="InvoiceNo">
          <DataField>InvoiceNo</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="InvoiceDate">
          <DataField>InvoiceDate</DataField>
          <ns1:TypeName>System.DateTime</ns1:TypeName>
        </Field>
        <Field Name="CustomerName">
          <DataField>CustomerName</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="GrandTotal">
          <DataField>GrandTotal</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
        <Field Name="TotalPaid">
          <DataField>TotalPaid</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
        <Field Name="Balance">
          <DataField>Balance</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Company">
      <Query>
        <DataSourceName>SalesDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CompanyName">
          <DataField>CompanyName</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="Address">
          <DataField>Address</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="ContactNo">
          <DataField>ContactNo</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <Body>
        <ReportItems>
          <Textbox Name="CompanyName">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CompanyName.Value, "Company")</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>16pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <ns1:DefaultName>CompanyName</ns1:DefaultName>
            <Top>0.25in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>7in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="ReportTitle">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>تقرير المبيعات</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>14pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <ns1:DefaultName>ReportTitle</ns1:DefaultName>
            <Top>0.75in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>7in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="DateRange">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="من " + Parameters!DateFrom.Value + " إلى " + Parameters!DateTo.Value</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>12pt</FontSize>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <ns1:DefaultName>DateRange</ns1:DefaultName>
            <Top>1.25in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>7in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Tablix Name="SalesTable">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvoiceNoHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>رقم الفاتورة</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ns1:DefaultName>InvoiceNoHeader</ns1:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  <TablixCell><CellContents><Textbox Name="EmptyCell_0"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell><TablixCell><CellContents><Textbox Name="EmptyCell_1"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell><TablixCell><CellContents><Textbox Name="EmptyCell_2"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell><TablixCell><CellContents><Textbox Name="EmptyCell_3"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell><TablixCell><CellContents><Textbox Name="EmptyCell_4"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell></TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>Sales</DataSetName>
            <Top>2in</Top>
            <Left>0.5in</Left>
            <Height>0.5in</Height>
            <Width>9in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>4in</Height>
        <Style />
      </Body>
      <Width>10in</Width>
      <Page>
        <PageHeight>11in</PageHeight>
        <PageWidth>8.5in</PageWidth>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
  <ReportParameters>
    <ReportParameter Name="DateFrom">
      <DataType>String</DataType>
      <Prompt>Date From</Prompt>
    </ReportParameter>
    <ReportParameter Name="DateTo">
      <DataType>String</DataType>
      <Prompt>Date To</Prompt>
    </ReportParameter>
    <ReportParameter Name="PrintDate">
      <DataType>String</DataType>
      <Prompt>Print Date</Prompt>
    </ReportParameter>
  </ReportParameters>
<ns1:ReportUnitType>Inch</ns1:ReportUnitType>
  <ns1:ReportID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</ns1:ReportID>
</Report>