﻿Imports System.Data.SqlClient
Imports System.Security.Cryptography
Imports System.IO
Imports System.Text
Public Class frmActivation

    ' دالة تحويل الأرقام العربية إلى إنجليزية
    Private Function ConvertArabicToEnglishNumbers(input As String) As String
        If String.IsNullOrEmpty(input) Then Return input

        Dim result As String = input
        ' تحويل الأرقام العربية ٠١٢٣٤٥٦٧٨٩ إلى إنجليزية 0123456789
        result = result.Replace("٠", "0")
        result = result.Replace("١", "1")
        result = result.Replace("٢", "2")
        result = result.Replace("٣", "3")
        result = result.Replace("٤", "4")
        result = result.Replace("٥", "5")
        result = result.Replace("٦", "6")
        result = result.Replace("٧", "7")
        result = result.Replace("٨", "8")
        result = result.Replace("٩", "9")

        ' تحويل الأرقام الفارسية ۰۱۲۳۴۵۶۷۸۹ إلى إنجليزية أيضاً
        result = result.Replace("۰", "0")
        result = result.Replace("۱", "1")
        result = result.Replace("۲", "2")
        result = result.Replace("۳", "3")
        result = result.Replace("۴", "4")
        result = result.Replace("۵", "5")
        result = result.Replace("۶", "6")
        result = result.Replace("۷", "7")
        result = result.Replace("۸", "8")
        result = result.Replace("۹", "9")

        Return result
    End Function
    Private Sub btnSave_Click(sender As System.Object, e As System.EventArgs) Handles btnSave.Click
        Try
            If txtHardwareID.Text = "" Then
                MessageBox.Show("Please enter hardware id", "", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtHardwareID.Focus()
                Exit Sub
            End If

            ' تحويل الأرقام العربية إلى إنجليزية قبل المعالجة
            Dim hardwareID As String = ConvertArabicToEnglishNumbers(txtHardwareID.Text.Trim())
            Dim serialNo As String = ConvertArabicToEnglishNumbers(txtSerialNo.Text.Trim())

            ' تحديث النصوص في الحقول
            txtHardwareID.Text = hardwareID
            txtSerialNo.Text = serialNo

            Dim st As String = hardwareID + serialNo
            txtActivationID.Text = Encryption.MakePassword(st, 216)
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub frmActivation_FormClosing(sender As System.Object, e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        End
    End Sub

    ' معالج لتحويل الأرقام العربية فوراً في Hardware ID
    Private Sub txtHardwareID_TextChanged(sender As Object, e As EventArgs) Handles txtHardwareID.TextChanged
        Try
            Dim currentPosition As Integer = txtHardwareID.SelectionStart
            Dim originalText As String = txtHardwareID.Text
            Dim convertedText As String = ConvertArabicToEnglishNumbers(originalText)

            If originalText <> convertedText Then
                txtHardwareID.Text = convertedText
                txtHardwareID.SelectionStart = currentPosition
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحويل
        End Try
    End Sub

    ' معالج لتحويل الأرقام العربية فوراً في Serial No
    Private Sub txtSerialNo_TextChanged(sender As Object, e As EventArgs) Handles txtSerialNo.TextChanged
        Try
            Dim currentPosition As Integer = txtSerialNo.SelectionStart
            Dim originalText As String = txtSerialNo.Text
            Dim convertedText As String = ConvertArabicToEnglishNumbers(originalText)

            If originalText <> convertedText Then
                txtSerialNo.Text = convertedText
                txtSerialNo.SelectionStart = currentPosition
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحويل
        End Try
    End Sub
End Class
