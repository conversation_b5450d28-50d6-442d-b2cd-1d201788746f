﻿Imports System.Data.SqlClient
Imports System.Security.Cryptography
Imports System.IO
Imports System.Text
Public Class frmActivation

    ' دالة تحويل الأرقام العربية إلى إنجليزية محسنة
    Private Function ConvertArabicToEnglishNumbers(input As String) As String
        If String.IsNullOrEmpty(input) Then Return ""

        Dim result As New System.Text.StringBuilder()

        For Each c As Char In input
            Select Case c
                ' الأرقام العربية ٠١٢٣٤٥٦٧٨٩
                Case "٠"c
                    result.Append("0")
                Case "١"c
                    result.Append("1")
                Case "٢"c
                    result.Append("2")
                Case "٣"c
                    result.Append("3")
                Case "٤"c
                    result.Append("4")
                Case "٥"c
                    result.Append("5")
                Case "٦"c
                    result.Append("6")
                Case "٧"c
                    result.Append("7")
                Case "٨"c
                    result.Append("8")
                Case "٩"c
                    result.Append("9")
                ' الأرقام الفارسية ۰۱۲۳۴۵۶۷۸۹
                Case "۰"c
                    result.Append("0")
                Case "۱"c
                    result.Append("1")
                Case "۲"c
                    result.Append("2")
                Case "۳"c
                    result.Append("3")
                Case "۴"c
                    result.Append("4")
                Case "۵"c
                    result.Append("5")
                Case "۶"c
                    result.Append("6")
                Case "۷"c
                    result.Append("7")
                Case "۸"c
                    result.Append("8")
                Case "۹"c
                    result.Append("9")
                ' الأرقام الهندية (أرقام أخرى)
                Case ChrW(&H966) To ChrW(&H96F) ' ٠-٩
                    result.Append(CStr(AscW(c) - &H966))
                Case ChrW(&H6F0) To ChrW(&H6F9) ' ۰-۹
                    result.Append(CStr(AscW(c) - &H6F0))
                Case Else
                    ' إبقاء الحرف كما هو إذا لم يكن رقماً عربياً
                    result.Append(c)
            End Select
        Next

        Return result.ToString()
    End Function
    ' معالج خاص للصق النص
    Protected Overrides Function ProcessCmdKey(ByRef msg As Message, keyData As Keys) As Boolean
        ' التحقق من Ctrl+V (لصق) في أي من الحقول
        If keyData = (Keys.Control Or Keys.V) Then
            Dim activeControl As Control = Me.ActiveControl
            If TypeOf activeControl Is TextBox Then
                Dim textBox As TextBox = CType(activeControl, TextBox)
                Try
                    ' الحصول على النص من الحافظة
                    If Clipboard.ContainsText() Then
                        Dim clipboardText As String = Clipboard.GetText()
                        Dim convertedText As String = ConvertArabicToEnglishNumbers(clipboardText)

                        ' لصق النص المحول
                        Dim selectionStart As Integer = textBox.SelectionStart
                        Dim selectionLength As Integer = textBox.SelectionLength

                        textBox.Text = textBox.Text.Remove(selectionStart, selectionLength).Insert(selectionStart, convertedText)
                        textBox.SelectionStart = selectionStart + convertedText.Length

                        Return True ' منع المعالجة الافتراضية للصق
                    End If
                Catch ex As Exception
                    ' تجاهل الأخطاء
                End Try
            End If
        End If

        Return MyBase.ProcessCmdKey(msg, keyData)
    End Function

    Private Sub btnSave_Click(sender As System.Object, e As System.EventArgs) Handles btnSave.Click
        Try
            If txtHardwareID.Text = "" Then
                MessageBox.Show("Please enter hardware id", "", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtHardwareID.Focus()
                Exit Sub
            End If

            ' تحويل الأرقام العربية إلى إنجليزية قبل المعالجة
            Dim hardwareID As String = ConvertArabicToEnglishNumbers(txtHardwareID.Text.Trim())
            Dim serialNo As String = ConvertArabicToEnglishNumbers(txtSerialNo.Text.Trim())

            ' تحديث النصوص في الحقول
            txtHardwareID.Text = hardwareID
            txtSerialNo.Text = serialNo

            Dim st As String = hardwareID + serialNo
            txtActivationID.Text = Encryption.MakePassword(st, 216)
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub frmActivation_FormClosing(sender As System.Object, e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        End
    End Sub

    ' معالج KeyPress للحقول
    Private Sub txtHardwareID_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtHardwareID.KeyPress
        ConvertArabicNumbersInKeyPress(e)
    End Sub

    Private Sub txtSerialNo_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtSerialNo.KeyPress
        ConvertArabicNumbersInKeyPress(e)
    End Sub

    Private Sub txtActivationID_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtActivationID.KeyPress
        ConvertArabicNumbersInKeyPress(e)
    End Sub

    ' دالة مساعدة لتحويل الأرقام في KeyPress
    Private Sub ConvertArabicNumbersInKeyPress(ByRef e As KeyPressEventArgs)
        Try
            Dim originalChar As Char = e.KeyChar

            Select Case originalChar
                Case "١"c : e.KeyChar = "1"c
                Case "٢"c : e.KeyChar = "2"c
                Case "٣"c : e.KeyChar = "3"c
                Case "٤"c : e.KeyChar = "4"c
                Case "٥"c : e.KeyChar = "5"c
                Case "٦"c : e.KeyChar = "6"c
                Case "٧"c : e.KeyChar = "7"c
                Case "٨"c : e.KeyChar = "8"c
                Case "٩"c : e.KeyChar = "9"c
                Case "٠"c : e.KeyChar = "0"c
                Case "۱"c : e.KeyChar = "1"c
                Case "۲"c : e.KeyChar = "2"c
                Case "۳"c : e.KeyChar = "3"c
                Case "۴"c : e.KeyChar = "4"c
                Case "۵"c : e.KeyChar = "5"c
                Case "۶"c : e.KeyChar = "6"c
                Case "۷"c : e.KeyChar = "7"c
                Case "۸"c : e.KeyChar = "8"c
                Case "۹"c : e.KeyChar = "9"c
                Case "۰"c : e.KeyChar = "0"c
            End Select
        Catch ex As Exception
        End Try
    End Sub

    ' معالج لتحويل الأرقام العربية فوراً في Hardware ID
    Private Sub txtHardwareID_TextChanged(sender As Object, e As EventArgs) Handles txtHardwareID.TextChanged
        Try
            Dim currentPosition As Integer = txtHardwareID.SelectionStart
            Dim originalText As String = txtHardwareID.Text
            Dim convertedText As String = ConvertArabicToEnglishNumbers(originalText)

            If originalText <> convertedText Then
                txtHardwareID.Text = convertedText
                txtHardwareID.SelectionStart = currentPosition
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحويل
        End Try
    End Sub

    ' معالج لتحويل الأرقام العربية فوراً في Serial No
    Private Sub txtSerialNo_TextChanged(sender As Object, e As EventArgs) Handles txtSerialNo.TextChanged
        Try
            Dim currentPosition As Integer = txtSerialNo.SelectionStart
            Dim originalText As String = txtSerialNo.Text
            Dim convertedText As String = ConvertArabicToEnglishNumbers(originalText)

            If originalText <> convertedText Then
                txtSerialNo.Text = convertedText
                txtSerialNo.SelectionStart = currentPosition
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحويل
        End Try
    End Sub
End Class
