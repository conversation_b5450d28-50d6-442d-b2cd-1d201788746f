<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:ns1="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="InvoiceDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <ns1:DataSourceID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</ns1:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="InvoiceInfo">
      <Query>
        <DataSourceName>InvoiceDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="InvoiceNo">
          <DataField>InvoiceNo</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="InvoiceDate">
          <DataField>InvoiceDate</DataField>
          <ns1:TypeName>System.DateTime</ns1:TypeName>
        </Field>
        <Field Name="CustomerName">
          <DataField>Name</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="GrandTotal">
          <DataField>GrandTotal</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
        <Field Name="TotalPaid">
          <DataField>TotalPaid</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
        <Field Name="Balance">
          <DataField>Balance</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="InvoiceProduct">
      <Query>
        <DataSourceName>InvoiceDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="ProductName">
          <DataField>ProductName</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="Qty">
          <DataField>Qty</DataField>
          <ns1:TypeName>System.Int32</ns1:TypeName>
        </Field>
        <Field Name="SellingPrice">
          <DataField>SellingPrice</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
        <Field Name="Amount">
          <DataField>Amount</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
        <Field Name="TotalAmount">
          <DataField>TotalAmount</DataField>
          <ns1:TypeName>System.Decimal</ns1:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Company">
      <Query>
        <DataSourceName>InvoiceDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CompanyName">
          <DataField>CompanyName</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="Address">
          <DataField>Address</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
        <Field Name="ContactNo">
          <DataField>ContactNo</DataField>
          <ns1:TypeName>System.String</ns1:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <Body>
        <ReportItems>
          <Textbox Name="CompanyName">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CompanyName.Value, "Company")</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>16pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <ns1:DefaultName>CompanyName</ns1:DefaultName>
            <Top>0.25in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>7in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="InvoiceTitle">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>فاتورة مبيعات</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>14pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <ns1:DefaultName>InvoiceTitle</ns1:DefaultName>
            <Top>0.75in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>7in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="InvoiceNo">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="رقم الفاتورة: " + First(Fields!InvoiceNo.Value, "InvoiceInfo")</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>12pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <ns1:DefaultName>InvoiceNo</ns1:DefaultName>
            <Top>1.25in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>3in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="InvoiceDate">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="التاريخ: " + Format(First(Fields!InvoiceDate.Value, "InvoiceInfo"), "dd/MM/yyyy")</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>12pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <ns1:DefaultName>InvoiceDate</ns1:DefaultName>
            <Top>1.25in</Top>
            <Left>4.5in</Left>
            <Height>0.25in</Height>
            <Width>3in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Tablix Name="ProductTable">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ProductNameHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>اسم المنتج</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <ns1:DefaultName>ProductNameHeader</ns1:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  <TablixCell><CellContents><Textbox Name="EmptyCell_0"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell><TablixCell><CellContents><Textbox Name="EmptyCell_1"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell><TablixCell><CellContents><Textbox Name="EmptyCell_2"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell><TablixCell><CellContents><Textbox Name="EmptyCell_3"><CanGrow>true</CanGrow><KeepTogether>true</KeepTogether><Paragraphs><Paragraph><TextRuns><TextRun><Value /><Style /></TextRun></TextRuns></Paragraph></Paragraphs></Textbox></CellContents></TablixCell></TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>InvoiceProduct</DataSetName>
            <Top>2in</Top>
            <Left>0.5in</Left>
            <Height>0.5in</Height>
            <Width>7.5in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>4in</Height>
        <Style />
      </Body>
      <Width>8.5in</Width>
      <Page>
        <PageHeight>11in</PageHeight>
        <PageWidth>8.5in</PageWidth>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
  <ReportParameters>
    <ReportParameter Name="InvoiceNo">
      <DataType>String</DataType>
      <Prompt>Invoice Number</Prompt>
    </ReportParameter>
    <ReportParameter Name="PrintDate">
      <DataType>String</DataType>
      <Prompt>Print Date</Prompt>
    </ReportParameter>
  </ReportParameters>

  <ns1:ReportUnitType>Inch</ns1:ReportUnitType>
  <ns1:ReportID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</ns1:ReportID>
</Report>