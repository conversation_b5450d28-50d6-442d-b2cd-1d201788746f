﻿Imports System.Data.SqlClient
Imports System.Security.Cryptography
Imports System.IO
Imports System.Text
Public Class frmActivation

    ' دالة تحويل الأرقام العربية إلى إنجليزية
    Private Function ConvertArabicToEnglishNumbers(input As String) As String
        If String.IsNullOrEmpty(input) Then Return input

        Dim result As String = input
        ' تحويل الأرقام العربية ٠١٢٣٤٥٦٧٨٩ إلى إنجليزية 0123456789
        result = result.Replace("٠", "0")
        result = result.Replace("١", "1")
        result = result.Replace("٢", "2")
        result = result.Replace("٣", "3")
        result = result.Replace("٤", "4")
        result = result.Replace("٥", "5")
        result = result.Replace("٦", "6")
        result = result.Replace("٧", "7")
        result = result.Replace("٨", "8")
        result = result.Replace("٩", "9")

        ' تحويل الأرقام الفارسية ۰۱۲۳۴۵۶۷۸۹ إلى إنجليزية أيضاً
        result = result.Replace("۰", "0")
        result = result.Replace("۱", "1")
        result = result.Replace("۲", "2")
        result = result.Replace("۳", "3")
        result = result.Replace("۴", "4")
        result = result.Replace("۵", "5")
        result = result.Replace("۶", "6")
        result = result.Replace("۷", "7")
        result = result.Replace("۸", "8")
        result = result.Replace("۹", "9")

        Return result
    End Function

    Private Sub frmActivation_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Try
            Dim i As System.Management.ManagementObject
            Dim searchInfo_Processor As New System.Management.ManagementObjectSearcher("Select * from Win32_Processor")
            For Each i In searchInfo_Processor.Get()
                txtHardwareID.Text = i("ProcessorID").ToString
            Next
            Dim searchInfo_Board As New System.Management.ManagementObjectSearcher("Select * from Win32_BaseBoard")
            For Each i In searchInfo_Board.Get()
                txtSerialNo.Text = i("SerialNumber").ToString
            Next
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "Error!")
            End
        End Try
    End Sub

    Private Sub btnSave_Click(sender As System.Object, e As System.EventArgs) Handles btnSave.Click
        Try
            If txtActivationID.Text = "" Then
                MessageBox.Show("من فضلك ادخل رقم التفعيل", "المبرمج", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtActivationID.Focus()
                Exit Sub
            End If

            ' تحويل الأرقام العربية إلى إنجليزية قبل المقارنة
            Dim activationCode As String = ConvertArabicToEnglishNumbers(txtActivationID.Text.Trim())
            txtActivationID.Text = activationCode ' تحديث النص في الحقل

            Dim st As String = (txtHardwareID.Text) + (txtSerialNo.Text)
            TextBox1.Text = Encryption.MakePassword(st, 216)
            If activationCode = TextBox1.Text Then
                con = New SqlConnection(cs)
                con.Open()
                Dim cb1 As String = "insert into Activation(HardwareID,SerialNo,ActivationID) VALUES (@d1,@d2,@d3)"
                cmd = New SqlCommand(cb1)
                cmd.Connection = con
                cmd.Parameters.AddWithValue("@d1", Encrypt(txtHardwareID.Text.Trim))
                cmd.Parameters.AddWithValue("@d2", Encrypt(txtSerialNo.Text.Trim))
                cmd.Parameters.AddWithValue("@d3", Encrypt(txtActivationID.Text.Trim()))
                cmd.ExecuteReader()
                con.Close()
                MessageBox.Show("تم التفعيل بنجاح", "المبرمج", MessageBoxButtons.OK, MessageBoxIcon.Information)
                frmLogin.Show()
                Me.Hide()
            Else
                MessageBox.Show("رقم تفعيل خاطئ...الرجاء التواصل مع المبرمج للحصول علي رقم التفعيل" & vbCrLf & "التواصل علي :" & vbCrLf & "Motasem Salem" & vbCrLf & "<EMAIL>" & vbCrLf & "Mobile No. +201062606098", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnClose_Click(sender As System.Object, e As System.EventArgs) Handles btnClose.Click
        End
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged

    End Sub

    Private Sub GroupBox1_Enter(sender As Object, e As EventArgs) Handles GroupBox1.Enter

    End Sub

    ' معالج لتحويل الأرقام العربية فوراً أثناء الكتابة
    Private Sub txtActivationID_TextChanged(sender As Object, e As EventArgs) Handles txtActivationID.TextChanged
        Try
            Dim currentPosition As Integer = txtActivationID.SelectionStart
            Dim originalText As String = txtActivationID.Text
            Dim convertedText As String = ConvertArabicToEnglishNumbers(originalText)

            If originalText <> convertedText Then
                txtActivationID.Text = convertedText
                txtActivationID.SelectionStart = currentPosition
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحويل
        End Try
    End Sub
End Class
