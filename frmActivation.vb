﻿Imports System.Data.SqlClient
Imports System.Security.Cryptography
Imports System.IO
Imports System.Text
Public Class frmActivation

    ' دالة تحويل الأرقام العربية إلى إنجليزية محسنة
    Private Function ConvertArabicToEnglishNumbers(input As String) As String
        If String.IsNullOrEmpty(input) Then Return ""

        Dim result As New System.Text.StringBuilder()

        For Each c As Char In input
            Select Case c
                ' الأرقام العربية ٠١٢٣٤٥٦٧٨٩
                Case "٠"c
                    result.Append("0")
                Case "١"c
                    result.Append("1")
                Case "٢"c
                    result.Append("2")
                Case "٣"c
                    result.Append("3")
                Case "٤"c
                    result.Append("4")
                Case "٥"c
                    result.Append("5")
                Case "٦"c
                    result.Append("6")
                Case "٧"c
                    result.Append("7")
                Case "٨"c
                    result.Append("8")
                Case "٩"c
                    result.Append("9")
                ' الأرقام الفارسية ۰۱۲۳۴۵۶۷۸۹
                Case "۰"c
                    result.Append("0")
                Case "۱"c
                    result.Append("1")
                Case "۲"c
                    result.Append("2")
                Case "۳"c
                    result.Append("3")
                Case "۴"c
                    result.Append("4")
                Case "۵"c
                    result.Append("5")
                Case "۶"c
                    result.Append("6")
                Case "۷"c
                    result.Append("7")
                Case "۸"c
                    result.Append("8")
                Case "۹"c
                    result.Append("9")
                ' الأرقام الهندية (أرقام أخرى)
                Case ChrW(&H966) To ChrW(&H96F) ' ٠-٩
                    result.Append(CStr(AscW(c) - &H966))
                Case ChrW(&H6F0) To ChrW(&H6F9) ' ۰-۹
                    result.Append(CStr(AscW(c) - &H6F0))
                Case Else
                    ' إبقاء الحرف كما هو إذا لم يكن رقماً عربياً
                    result.Append(c)
            End Select
        Next

        Return result.ToString()
    End Function

    ' دالة إضافية لفرض الأرقام الإنجليزية
    Private Function ForceEnglishNumbers(input As String) As String
        If String.IsNullOrEmpty(input) Then Return ""

        Dim result As String = input

        ' تحويل مضاعف للتأكد
        For i As Integer = 0 To 9
            Dim arabicDigit As String = ChrW(&H660 + i) ' ٠-٩
            Dim persianDigit As String = ChrW(&H6F0 + i) ' ۰-۹
            result = result.Replace(arabicDigit, i.ToString())
            result = result.Replace(persianDigit, i.ToString())
        Next

        ' تحويل يدوي إضافي للأرقام الشائعة
        result = result.Replace("٠", "0").Replace("١", "1").Replace("٢", "2").Replace("٣", "3").Replace("٤", "4")
        result = result.Replace("٥", "5").Replace("٦", "6").Replace("٧", "7").Replace("٨", "8").Replace("٩", "9")
        result = result.Replace("۰", "0").Replace("۱", "1").Replace("۲", "2").Replace("۳", "3").Replace("۴", "4")
        result = result.Replace("۵", "5").Replace("۶", "6").Replace("۷", "7").Replace("۸", "8").Replace("۹", "9")

        Return result
    End Function

    ' معالج خاص للصق النص محسن
    Protected Overrides Function ProcessCmdKey(ByRef msg As Message, keyData As Keys) As Boolean
        ' التحقق من Ctrl+V (لصق)
        If keyData = (Keys.Control Or Keys.V) AndAlso txtActivationID.Focused Then
            Try
                ' الحصول على النص من الحافظة
                If Clipboard.ContainsText() Then
                    Dim clipboardText As String = Clipboard.GetText()

                    ' تحويل متقدم للنص
                    Dim convertedText As String = ConvertArabicToEnglishNumbers(clipboardText)

                    ' تحويل إضافي للتأكد من الأرقام في البداية
                    convertedText = ForceEnglishNumbers(convertedText)

                    ' مسح الحقل بالكامل ولصق النص الجديد
                    txtActivationID.Text = ""
                    txtActivationID.Text = convertedText
                    txtActivationID.SelectionStart = convertedText.Length

                    ' تحديث فوري إضافي
                    Application.DoEvents()
                    txtActivationID.Text = ConvertArabicToEnglishNumbers(txtActivationID.Text)

                    Return True ' منع المعالجة الافتراضية للصق
                End If
            Catch ex As Exception
                ' تجاهل الأخطاء
            End Try
        End If

        Return MyBase.ProcessCmdKey(msg, keyData)
    End Function

    Private Sub frmActivation_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Try
            ' محاولة تعيين Input Language إلى الإنجليزية
            Try
                For Each lang As System.Windows.Forms.InputLanguage In System.Windows.Forms.InputLanguage.InstalledInputLanguages
                    If lang.Culture.Name.StartsWith("en") Then
                        System.Windows.Forms.InputLanguage.CurrentInputLanguage = lang
                        Exit For
                    End If
                Next
            Catch
                ' تجاهل إذا فشل
            End Try

            ' تفعيل Timer للتحويل المستمر
            AddHandler conversionTimer.Tick, AddressOf ConversionTimer_Tick
            conversionTimer.Start()

            Dim i As System.Management.ManagementObject
            Dim searchInfo_Processor As New System.Management.ManagementObjectSearcher("Select * from Win32_Processor")
            For Each i In searchInfo_Processor.Get()
                txtHardwareID.Text = i("ProcessorID").ToString
            Next
            Dim searchInfo_Board As New System.Management.ManagementObjectSearcher("Select * from Win32_BaseBoard")
            For Each i In searchInfo_Board.Get()
                txtSerialNo.Text = i("SerialNumber").ToString
            Next
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "Error!")
            End
        End Try
    End Sub

    ' معالج Timer للتحويل المستمر
    Private Sub ConversionTimer_Tick(sender As Object, e As EventArgs)
        If Not isConverting AndAlso txtActivationID.Focused Then
            Try
                isConverting = True
                Dim originalText As String = txtActivationID.Text
                Dim convertedText As String = ForceEnglishNumbers(originalText)
                If originalText <> convertedText Then
                    Dim cursorPos As Integer = txtActivationID.SelectionStart
                    txtActivationID.Text = convertedText
                    txtActivationID.SelectionStart = Math.Min(cursorPos, txtActivationID.Text.Length)
                End If
            Catch ex As Exception
                ' تجاهل الأخطاء
            Finally
                isConverting = False
            End Try
        End If
    End Sub

    Private Sub btnSave_Click(sender As System.Object, e As System.EventArgs) Handles btnSave.Click
        Try
            If txtActivationID.Text = "" Then
                MessageBox.Show("من فضلك ادخل رقم التفعيل", "المبرمج", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtActivationID.Focus()
                Exit Sub
            End If

            ' تحويل الأرقام العربية إلى إنجليزية قبل المقارنة
            Dim activationCode As String = ConvertArabicToEnglishNumbers(txtActivationID.Text.Trim())
            txtActivationID.Text = activationCode ' تحديث النص في الحقل

            Dim st As String = (txtHardwareID.Text) + (txtSerialNo.Text)
            TextBox1.Text = Encryption.MakePassword(st, 216)
            If activationCode = TextBox1.Text Then
                con = New SqlConnection(cs)
                con.Open()
                Dim cb1 As String = "insert into Activation(HardwareID,SerialNo,ActivationID) VALUES (@d1,@d2,@d3)"
                cmd = New SqlCommand(cb1)
                cmd.Connection = con
                cmd.Parameters.AddWithValue("@d1", Encrypt(txtHardwareID.Text.Trim))
                cmd.Parameters.AddWithValue("@d2", Encrypt(txtSerialNo.Text.Trim))
                cmd.Parameters.AddWithValue("@d3", Encrypt(txtActivationID.Text.Trim()))
                cmd.ExecuteReader()
                con.Close()
                MessageBox.Show("تم التفعيل بنجاح", "المبرمج", MessageBoxButtons.OK, MessageBoxIcon.Information)
                frmLogin.Show()
                Me.Hide()
            Else
                MessageBox.Show("رقم تفعيل خاطئ...الرجاء التواصل مع المبرمج للحصول علي رقم التفعيل" & vbCrLf & "التواصل علي :" & vbCrLf & "Motasem Salem" & vbCrLf & "<EMAIL>" & vbCrLf & "Mobile No. +201062606098", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnClose_Click(sender As System.Object, e As System.EventArgs) Handles btnClose.Click
        End
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged

    End Sub

    Private Sub GroupBox1_Enter(sender As Object, e As EventArgs) Handles GroupBox1.Enter

    End Sub

    ' متغير لمنع التكرار اللانهائي
    Private isConverting As Boolean = False

    ' Timer للتحويل المستمر
    Private conversionTimer As New Timer() With {.Interval = 100, .Enabled = False}

    ' معالج لتحويل الأرقام العربية فوراً أثناء الكتابة
    Private Sub txtActivationID_TextChanged(sender As Object, e As EventArgs) Handles txtActivationID.TextChanged
        If isConverting Then Return ' منع التكرار اللانهائي

        Try
            isConverting = True
            Dim currentPosition As Integer = txtActivationID.SelectionStart
            Dim originalText As String = txtActivationID.Text

            ' تحويل مضاعف للتأكد
            Dim convertedText As String = ConvertArabicToEnglishNumbers(originalText)
            convertedText = ForceEnglishNumbers(convertedText)

            If originalText <> convertedText Then
                txtActivationID.Text = convertedText
                txtActivationID.SelectionStart = Math.Min(currentPosition, txtActivationID.Text.Length)
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في التحويل
        Finally
            isConverting = False
        End Try
    End Sub

    ' معالج لمنع إدخال الأرقام العربية من الأساس
    Private Sub txtActivationID_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtActivationID.KeyPress
        Try
            Dim originalChar As Char = e.KeyChar

            ' تحويل مباشر للأرقام العربية والفارسية
            Select Case originalChar
                Case "١"c : e.KeyChar = "1"c
                Case "٢"c : e.KeyChar = "2"c
                Case "٣"c : e.KeyChar = "3"c
                Case "٤"c : e.KeyChar = "4"c
                Case "٥"c : e.KeyChar = "5"c
                Case "٦"c : e.KeyChar = "6"c
                Case "٧"c : e.KeyChar = "7"c
                Case "٨"c : e.KeyChar = "8"c
                Case "٩"c : e.KeyChar = "9"c
                Case "٠"c : e.KeyChar = "0"c
                Case "۱"c : e.KeyChar = "1"c
                Case "۲"c : e.KeyChar = "2"c
                Case "۳"c : e.KeyChar = "3"c
                Case "۴"c : e.KeyChar = "4"c
                Case "۵"c : e.KeyChar = "5"c
                Case "۶"c : e.KeyChar = "6"c
                Case "۷"c : e.KeyChar = "7"c
                Case "۸"c : e.KeyChar = "8"c
                Case "۹"c : e.KeyChar = "9"c
                Case "۰"c : e.KeyChar = "0"c
                Case Else
                    ' تحويل باستخدام الدالة للحالات الأخرى
                    Dim convertedChar As String = ConvertArabicToEnglishNumbers(originalChar.ToString())
                    If convertedChar <> originalChar.ToString() AndAlso convertedChar.Length = 1 Then
                        e.KeyChar = convertedChar(0)
                    End If
            End Select
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    ' معالج للـ KeyDown لمعالجة حالات خاصة
    Private Sub txtActivationID_KeyDown(sender As Object, e As KeyEventArgs) Handles txtActivationID.KeyDown
        Try
            ' فرض استخدام الأرقام الإنجليزية
            If e.KeyCode >= Keys.D0 AndAlso e.KeyCode <= Keys.D9 Then
                ' الأرقام من الصف العلوي
                Return
            ElseIf e.KeyCode >= Keys.NumPad0 AndAlso e.KeyCode <= Keys.NumPad9 Then
                ' الأرقام من NumPad
                Return
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    ' معالج للصق النص مع تحويل الأرقام
    Private Sub txtActivationID_Enter(sender As Object, e As EventArgs) Handles txtActivationID.Enter
        ' تحويل النص عند دخول الحقل
        Try
            ' محاولة تغيير Input Method إلى الإنجليزية
            Try
                System.Windows.Forms.InputLanguage.CurrentInputLanguage = System.Windows.Forms.InputLanguage.DefaultInputLanguage
            Catch
                ' تجاهل إذا فشل
            End Try

            ' تحويل مضاعف للنص الموجود
            Dim originalText As String = txtActivationID.Text
            Dim convertedText As String = ConvertArabicToEnglishNumbers(originalText)
            convertedText = ForceEnglishNumbers(convertedText)

            If originalText <> convertedText Then
                txtActivationID.Text = convertedText
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    ' معالج إضافي للتأكد من التحويل عند فقدان التركيز
    Private Sub txtActivationID_Leave(sender As Object, e As EventArgs) Handles txtActivationID.Leave
        Try
            Dim originalText As String = txtActivationID.Text
            Dim convertedText As String = ForceEnglishNumbers(originalText)
            If originalText <> convertedText Then
                txtActivationID.Text = convertedText
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    ' معالج للنقر على الحقل
    Private Sub txtActivationID_Click(sender As Object, e As EventArgs) Handles txtActivationID.Click
        Try
            ' تحويل فوري عند النقر
            Dim originalText As String = txtActivationID.Text
            Dim convertedText As String = ForceEnglishNumbers(originalText)
            If originalText <> convertedText Then
                txtActivationID.Text = convertedText
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub
End Class
