﻿Imports System.Data.SqlClient
Imports System.Security.Cryptography
Imports System.IO
Imports System.Text
Public Class frmActivation

    Private Sub frmActivation_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Try
            ' إعداد حقل التفعيل لقبول الأرقام الإنجليزية فقط
            ConfigureActivationField()

            Dim i As System.Management.ManagementObject
            Dim searchInfo_Processor As New System.Management.ManagementObjectSearcher("Select * from Win32_Processor")
            For Each i In searchInfo_Processor.Get()
                txtHardwareID.Text = i("ProcessorID").ToString
            Next
            Dim searchInfo_Board As New System.Management.ManagementObjectSearcher("Select * from Win32_BaseBoard")
            For Each i In searchInfo_Board.Get()
                txtSerialNo.Text = i("SerialNumber").ToString
            Next
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "Error!")
            End
        End Try
    End Sub

    Private Sub btnSave_Click(sender As System.Object, e As System.EventArgs) Handles btnSave.Click
        Try
            If txtActivationID.Text = "" Then
                MessageBox.Show("من فضلك ادخل رقم التفعيل", "المبرمج", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtActivationID.Focus()
                Exit Sub
            End If

            ' تنظيف البيانات وتحويل الأرقام العربية
            Dim enteredCode As String = ProcessTextForEnglishOnly(txtActivationID.Text.Trim()).Replace(" ", "").ToUpper()
            Dim hardwareID As String = ProcessTextForEnglishOnly(txtHardwareID.Text.Trim()).Replace(" ", "")
            Dim serialNo As String = ProcessTextForEnglishOnly(txtSerialNo.Text.Trim()).Replace(" ", "")

            ' توليد الكود المتوقع بالطريقة البسيطة
            Dim combined As String = hardwareID + serialNo
            Dim expectedCode As String = GenerateSimpleCode(combined)

            ' عرض التشخيص
            MessageBox.Show("Hardware ID: " & hardwareID & vbCrLf & _
                          "Serial No: " & serialNo & vbCrLf & _
                          "Combined: " & combined & vbCrLf & _
                          "Expected Code: " & expectedCode & vbCrLf & _
                          "Entered Code: " & enteredCode & vbCrLf & _
                          "Match: " & (enteredCode = expectedCode).ToString(), _
                          "Activation Debug", MessageBoxButtons.OK, MessageBoxIcon.Information)

            If enteredCode = expectedCode Then
                con = New SqlConnection(cs)
                con.Open()
                Dim cb1 As String = "insert into Activation(HardwareID,SerialNo,ActivationID) VALUES (@d1,@d2,@d3)"
                cmd = New SqlCommand(cb1)
                cmd.Connection = con
                cmd.Parameters.AddWithValue("@d1", Encrypt(txtHardwareID.Text.Trim))
                cmd.Parameters.AddWithValue("@d2", Encrypt(txtSerialNo.Text.Trim))
                cmd.Parameters.AddWithValue("@d3", Encrypt(txtActivationID.Text.Trim()))
                cmd.ExecuteReader()
                con.Close()
                MessageBox.Show("تم التفعيل بنجاح", "المبرمج", MessageBoxButtons.OK, MessageBoxIcon.Information)
                frmLogin.Show()
                Me.Hide()
            Else
                MessageBox.Show("رقم تفعيل خاطئ...الرجاء التواصل مع المبرمج للحصول علي رقم التفعيل" & vbCrLf & "التواصل علي :" & vbCrLf & "Motasem Salem" & vbCrLf & "<EMAIL>" & vbCrLf & "Mobile No. +201062606098", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة توليد كود بسيط (نفس الدالة في أداة التفعيل)
    Private Function GenerateSimpleCode(input As String) As String
        Try
            ' كود بسيط جداً - أول 10 أحرف + طولها
            Dim result As String = ""
            If input.Length >= 10 Then
                result = input.Substring(0, 10) + input.Length.ToString()
            Else
                result = input + input.Length.ToString()
            End If
            Return result.ToUpper()
        Catch ex As Exception
            Return "ERROR"
        End Try
    End Function

    Private Sub btnClose_Click(sender As System.Object, e As System.EventArgs) Handles btnClose.Click
        End
    End Sub

    Private Sub TextBox2_TextChanged(sender As Object, e As EventArgs) Handles TextBox2.TextChanged

    End Sub

    ' معالج لمنع إدخال الأرقام العربية من الأساس
    Private Sub txtActivationID_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtActivationID.KeyPress
        Try
            Dim originalChar As Char = e.KeyChar
            Dim charCode As Integer = AscW(originalChar)

            ' فحص شامل للأرقام العربية والفارسية ومنعها
            If (charCode >= &H660 AndAlso charCode <= &H669) OrElse _
               (charCode >= &H6F0 AndAlso charCode <= &H6F9) Then

                ' تحويل مباشر للرقم الإنجليزي
                If charCode >= &H660 AndAlso charCode <= &H669 Then
                    e.KeyChar = ChrW(Asc("0") + (charCode - &H660))
                ElseIf charCode >= &H6F0 AndAlso charCode <= &H6F9 Then
                    e.KeyChar = ChrW(Asc("0") + (charCode - &H6F0))
                End If

            Else
                ' تحويل مباشر للأرقام الشائعة
                Select Case originalChar
                    Case "١"c : e.KeyChar = "1"c
                    Case "٢"c : e.KeyChar = "2"c
                    Case "٣"c : e.KeyChar = "3"c
                    Case "٤"c : e.KeyChar = "4"c
                    Case "٥"c : e.KeyChar = "5"c
                    Case "٦"c : e.KeyChar = "6"c
                    Case "٧"c : e.KeyChar = "7"c
                    Case "٨"c : e.KeyChar = "8"c
                    Case "٩"c : e.KeyChar = "9"c
                    Case "٠"c : e.KeyChar = "0"c
                    Case "۱"c : e.KeyChar = "1"c
                    Case "۲"c : e.KeyChar = "2"c
                    Case "۳"c : e.KeyChar = "3"c
                    Case "۴"c : e.KeyChar = "4"c
                    Case "۵"c : e.KeyChar = "5"c
                    Case "۶"c : e.KeyChar = "6"c
                    Case "۷"c : e.KeyChar = "7"c
                    Case "۸"c : e.KeyChar = "8"c
                    Case "۹"c : e.KeyChar = "9"c
                    Case "۰"c : e.KeyChar = "0"c
                End Select
            End If

        Catch ex As Exception
        End Try
    End Sub

    ' متغير لمنع التكرار اللانهائي
    Private isConverting As Boolean = False

    ' معالج لتحويل الأرقام العربية فوراً أثناء الكتابة
    Private Sub txtActivationID_TextChanged(sender As Object, e As EventArgs) Handles txtActivationID.TextChanged
        If isConverting Then Return

        Try
            isConverting = True
            Dim currentPosition As Integer = txtActivationID.SelectionStart
            Dim originalText As String = txtActivationID.Text

            ' تحويل شامل باستخدام الدالة الجديدة
            Dim convertedText As String = ProcessTextForEnglishOnly(originalText)

            If originalText <> convertedText Then
                txtActivationID.Text = convertedText
                txtActivationID.SelectionStart = Math.Min(currentPosition, txtActivationID.Text.Length)

                ' إعادة تعيين خصائص الحقل للتأكد
                txtActivationID.ImeMode = ImeMode.Disable
                txtActivationID.RightToLeft = RightToLeft.No
            End If
        Catch ex As Exception
        Finally
            isConverting = False
        End Try
    End Sub

    ' معالج خاص للصق النص محسن ومتقدم
    Protected Overrides Function ProcessCmdKey(ByRef msg As Message, keyData As Keys) As Boolean
        If keyData = (Keys.Control Or Keys.V) AndAlso txtActivationID.Focused Then
            Try
                If Clipboard.ContainsText() Then
                    Dim clipboardText As String = Clipboard.GetText()

                    ' تحويل فوري ومتعدد المراحل
                    Dim convertedText As String = ProcessTextForEnglishOnly(clipboardText)

                    ' تعطيل جميع الأحداث مؤقتاً
                    isConverting = True

                    ' مسح الحقل وإعادة تعيين الخصائص
                    txtActivationID.Text = ""
                    txtActivationID.ImeMode = ImeMode.Disable
                    txtActivationID.RightToLeft = RightToLeft.No

                    ' لصق النص المحول
                    txtActivationID.Text = convertedText
                    txtActivationID.SelectionStart = convertedText.Length

                    ' تحديث فوري متعدد
                    Application.DoEvents()
                    System.Threading.Thread.Sleep(10)
                    txtActivationID.Text = ProcessTextForEnglishOnly(txtActivationID.Text)

                    isConverting = False

                    Return True ' منع المعالجة الافتراضية للصق
                End If
            Catch ex As Exception
                isConverting = False
            End Try
        End If

        Return MyBase.ProcessCmdKey(msg, keyData)
    End Function

    Private Sub GroupBox1_Enter(sender As Object, e As EventArgs) Handles GroupBox1.Enter

    End Sub

    ' دالة شاملة لمعالجة النص وضمان الأرقام الإنجليزية فقط
    Private Function ProcessTextForEnglishOnly(input As String) As String
        If String.IsNullOrEmpty(input) Then Return ""

        Dim result As New System.Text.StringBuilder()

        For Each c As Char In input
            ' تحويل مباشر للأرقام العربية والفارسية
            Select Case c
                Case "٠"c, "۰"c : result.Append("0")
                Case "١"c, "۱"c : result.Append("1")
                Case "٢"c, "۲"c : result.Append("2")
                Case "٣"c, "۳"c : result.Append("3")
                Case "٤"c, "۴"c : result.Append("4")
                Case "٥"c, "۵"c : result.Append("5")
                Case "٦"c, "۶"c : result.Append("6")
                Case "٧"c, "۷"c : result.Append("7")
                Case "٨"c, "۸"c : result.Append("8")
                Case "٩"c, "۹"c : result.Append("9")
                Case Else
                    ' فحص إضافي للأرقام Unicode
                    Dim charCode As Integer = AscW(c)
                    If charCode >= &H660 AndAlso charCode <= &H669 Then ' ٠-٩
                        result.Append(CStr(charCode - &H660))
                    ElseIf charCode >= &H6F0 AndAlso charCode <= &H6F9 Then ' ۰-۹
                        result.Append(CStr(charCode - &H6F0))
                    Else
                        ' إبقاء الحرف كما هو إذا لم يكن رقماً عربياً
                        result.Append(c)
                    End If
            End Select
        Next

        Return result.ToString()
    End Function

    ' دالة إعداد حقل التفعيل لقبول الأرقام الإنجليزية فقط
    Private Sub ConfigureActivationField()
        Try
            ' تعيين IME Mode لمنع الأرقام العربية
            txtActivationID.ImeMode = ImeMode.Disable

            ' تعيين خصائص إضافية
            txtActivationID.RightToLeft = RightToLeft.No

            ' فرض الاتجاه من اليسار لليمين
            txtActivationID.TextAlign = HorizontalAlignment.Left

        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub
End Class
