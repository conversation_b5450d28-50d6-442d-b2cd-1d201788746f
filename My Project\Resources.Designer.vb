'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources

    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute()>  _
    Friend Class Resources

        Private Shared resourceMan As Global.System.Resources.ResourceManager

        Private Shared resourceCulture As Global.System.Globalization.CultureInfo

        <Global.System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")>  _
        Friend Sub New()
            MyBase.New
        End Sub

        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Shared ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("Sales_and_Inventory_System.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property

        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Shared Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property _12() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("_12", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Actions_user_group_new_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Actions_user_group_new_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Activate() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Activate", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Admin_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Admin_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property basket_full_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("basket_full_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Billing_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Billing_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Button_Delete_icon1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Button_Delete_icon1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Button_Delete_icon11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Button_Delete_icon11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Close_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Close_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Company1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Company1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Database_Active_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Database_Active_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Database_Active_icon1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Database_Active_icon1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property edit_file_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("edit_file_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Entypo_d83d_0__512() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Entypo_d83d_0__512", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Excel_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Excel_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Inventory_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Inventory_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property keyboard_icon__1_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("keyboard_icon__1_", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property log_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("log_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Log_Out_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Log_Out_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Maximise_32X32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Maximise_32X32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property messages_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("messages_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property ModernXP_09_Keyboard_icon__1_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ModernXP_09_Keyboard_icon__1_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property panelControl1_ContentImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("panelControl1_ContentImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property panelControl11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("panelControl11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property payment_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("payment_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property photo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Picsart_23_03_19_11_27_15_052() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Picsart_23_03_19_11_27_15_052", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Picsart_23_03_19_11_27_15_0522() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Picsart_23_03_19_11_27_15_0522", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property product_sales_report_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("product_sales_report_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Programming_Minimize_Window_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Programming_Minimize_Window_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property report_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("report_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Reset2_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Reset2_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Stocks_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Stocks_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property User_Group_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("User_Group_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property User_Interface_Restore_Window_icon__1_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("User_Interface_Restore_Window_icon__1_", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Users_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Users_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Utilities_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Utilities_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Class
End Namespace
