﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Class Resources
        
        Private Shared resourceMan As Global.System.Resources.ResourceManager

        Private Shared resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Shared ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("Sales_and_Inventory_System.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Shared Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property _1__16_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("1 (16)", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property _12() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("12", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Action_Security_ChangePassword_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Action_Security_ChangePassword_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Actions_user_group_new_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Actions-user-group-new-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property

        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend Shared ReadOnly Property Activate() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Activate", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property add_stock() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add stock", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Admin_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Admin-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Apply_16x16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Apply_16x16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property background_screen() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("background screen", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property basket_full_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("basket-full-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Billing() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Billing", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Billing_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Billing-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Button_Close_icon__1_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Button-Close-icon (1)", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Button_Delete_icon1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Button-Delete-icon1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Button_Delete_icon11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Button-Delete-icon11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property cancel_512() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("cancel-512", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Close_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Close_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Company1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Company1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Database() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Database", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Database_Active_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Database-Active-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Database_Active_icon1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Database-Active-icon1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property edit_file_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("edit-file-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Entypo_d83d_0__512() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Entypo_d83d(0)_512", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Entypo_e731_0__512() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Entypo_e731(0)_512", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Excel_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Excel-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property fevicon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("fevicon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property find_customer() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("find customer", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Hotels_B_512() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Hotels_B-512", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Inventory_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Inventory-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property keyboard_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("keyboard-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property keyboard_icon__1_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("keyboard-icon (1)", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property log_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("log-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Log_Out_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Log-Out-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property login_512() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("login-512", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property login_icon__1_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("login_icon (1)", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property logout() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("logout", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Logs() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Logs", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Maximise_32X32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Maximise-32X32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property messages_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("messages-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property ModernXP_09_Keyboard_icon__1_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ModernXP-09-Keyboard-icon (1)1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property money_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("money-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property new_customers() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new customers", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property packages__2_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("packages (2)", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property panelControl1_ContentImage() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("panelControl1.ContentImage", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property panelControl11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("panelControl11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property payment_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("payment-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property peImage_EditValue() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("peImage.EditValue", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property peImage1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("peImage1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property peImage2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("peImage2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property peImage3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("peImage3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property photo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Picsart_23_03_19_11_27_15_052() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Picsart_23-03-19_11-27-15-052", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Picsart_23_03_19_11_27_15_0521() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Picsart_23-03-19_11-27-15-0521", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Picsart_23_03_19_11_27_15_0522() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Picsart_23-03-19_11-27-15-0522", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Product() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Product", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property product_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("product-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property product_sales_report_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("product-sales-report-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Programming_Minimize_Window_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Programming-Minimize-Window-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property quotation_256() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("quotation 256", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property record_512() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("record_512", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property report_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("report-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property reports() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("reports", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property reports1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("reports1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Reset2_32x32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Reset2_32x32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property search_invoice() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("search invoice", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property service_256() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("service 256", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property splash() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("splash", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property stock_in_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("stock in icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Stocks_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Stocks-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Summary() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Summary", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property supplier() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("supplier", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property User_Group_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("User-Group-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property User_Interface_Restore_Window_icon__1_() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("User-Interface-Restore-Window-icon (1)", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property user_regestration() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("user regestration", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Users_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Users-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Utilities_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Utilities-icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Voucher() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Voucher", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property warehouse_illu_01() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("warehouse_illu_01", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
