<?xml version="1.0" standalone="yes"?>
<xs:schema id="NewDataSet" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="NewDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Table1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ProductCode" type="xs:string" minOccurs="0" />
              <xs:element name="ProductName" type="xs:string" minOccurs="0" />
              <xs:element name="SellingPrice" type="xs:decimal" minOccurs="0" />
              <xs:element name="Discount" type="xs:decimal" minOccurs="0" />
              <xs:element name="VAT" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>