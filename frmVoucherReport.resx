﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="Timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAQEAAAAAAGAAoMgAAFgAAACgAAABAAAAAgAAAAAEAGAAAAAAAADAAAAAAAAAAAAAAAAAAAAAA
        AAD/////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7/////////////
        ///////////////////////////+/v7+/v7+/v7/////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        /////////////////////////////////////v7+/v7+/v7/////////////////////////////////
        ///////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v75/P7N6Puv2vqs2fqs2fqs2fqs2fqs2fqs2fqs
        2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs
        2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fqs2fq/4vvv+P3+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v77/f6Y0PkemfIAjPEAjPEBjPEBjPEA
        jPEAjPEAjPEAjPEBjPEAjPEAjPEAjPEAjPEAjPEBjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEBjPEBjPEB
        jPEAjPEAjPEAjPEBjPEAjPEAjPEBi/EAjPEAjPEBi/EAjPEAjPEAjPEAjPEAjPEBjPEAjPEAjPEKkPFq
        u/bq9f3+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v75/P5ZtPUBjPEAjPEA
        jPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAi/AAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAi/AAi/AA
        i/AAjPEAjPEAjPEAjPEAjPEAjPEAi/AAjPEAjPEAi/AAi/AAi/AAjPEAi/AAjPEAjPEAjPEAi/AAjPEA
        jPEAjPEAjPEAi/ABjPEjm/Pc7/z+/v7+/v7////+/v7+/v7+/v7////////////////////+/v7+/v5/
        xfcCjfEBjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEA
        jPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEA
        jPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEDjfEyovP4+/7+/v7////////////////////+/v7+/v7+
        /v7////+/v7n9P0MkfEDjfEAjPAAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/ABjPEDjfGf0/n+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v6c0vkGjvEBjPEAjPEAi/AAi/AAjPEAjPEAjPEAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/EAi/AAjPEAi/AAi/AAi/AAjPEHj/FKrfT+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v51wPcKkPEAjPEAi/AAi/AAi/EBjPEdmPIMkfEA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/EQk/Eal/IAjPEAi/AAi/AA
        i/AAjPEIj/Ejm/L+/v7////+/v7+/v7+/v7///////////////////////9uvfcNkfEAjPAAjPEAjPEA
        jPGWz/n9/v7r9v0xofMAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEA
        jPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPFCqfTw+P37
        /f6BxvgAjPEAjPEAjPEAjPEKkPEdmfL////////////////////////+/v7+/v7+/v7////+/v5uvfcQ
        k/IAi/AAi/AAi/AKkPHy+f7+/v7+/v6FyPgAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPGc0vn+/v7+/v7n9P0Ai/AAi/AAi/AAjPEMkfEemfL+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v5uvfcTlfIAjPEAi/AAi/ALkfH0+v7+/v7+/v6JyfgAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPGe0/n+/v7+/v7q9f0Ai/AAi/AAi/AAjPAPkvEemfL+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v5vvfcXlvIAi/EAi/AAi/ALkfH0+v7+/v7+/v694ftzwPdzv/dzv/dz
        v/dzwPdzv/dzv/dzv/dzwPdzv/dzv/dzv/dzwPdzv/dzv/dzv/dzwPdzv/dzv/dzv/dzwPdzv/dzv/dz
        v/dzwPdzv/dzv/dzv/dzwPdzv/dzv/dzv/dzwPfJ5/v+/v7+/v7q9f0Ai/AAi/AAi/AAjPASlPIfmvL+
        /v7////+/v7+/v7+/v7///////////////////////9uvvcbmPIAjPEAjPEAjPELkfH1+v7////+//7+
        /v7/////////////////////////////////////////////////////////////////////////////
        ///////////////////////////////////////////////////////+/v7+/v7////q9f0AjPEAjPEA
        jPEAjPAVlfIgmvL//v/////////////////////+/v7+/v7+/v7////+/v5uvvYfmvMAi/EAi/AAi/AL
        kfH0+v7+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7q9f0Ai/AAi/AAi/AAjPEYlvIhm/L+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvfYi
        m/MAjPAAi/AAi/ALkfH0+v7+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7q9f0Ai/AAi/AAi/AAjPEbmPIim/L+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v5uvfYmnfIAjPEAi/AAi/ALkfH0+v7+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7q9f0Ai/AAi/AAi/AAjPEemfIjm/L+/v7////+/v7+/v7+
        /v7///////////////////////9uvvcqn/MAjPEAjPEAjPELkfH1+v7/////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////////////////////////////////q9f0AjPEAjPEAjPEAjPEhm/IknPL/
        ///////////////////////+/v7+/v7+/v7////+/v5uvvcvofMAjPEAi/AAi/ALkfH0+v7+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7q9f0Ai/AAi/AA
        i/AAjPEknPIknPP+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvfYzo/QAjPEAi/AAi/AL
        kfH0+v7+/v7+/v7+/v7//v/+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7//v/+/v7+/v7+
        /v7//v/+/v7+/v7+/v7//v/+/v7+/v7+/v7//v/+/v7+/v7+/v7//v/+/v7+/v7+/v7//v/+/v7+/v7+
        /v7q9f0Ai/AAi/AAi/AAjPEonvMlnfL+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvvY3
        pPQAjPEAi/AAi/ALkfH0+v7+/v7+/v6w2/oknPMknPIknPIknPJOr/X+/v5VsvX+/v6b0vkknPIknPIk
        nPIknPMknPIknPIknPIknPMknPIknPIknPIknPMknPIknPIknPIknPMknPIknPIknPIknPMknPIknPIk
        nPIknPOs2fr+/v7+/v7q9f0Ai/AAi/AAi/AAjPErn/MmnfP+/v7////+/v7+/v7+/v7/////////////
        //////////9uvvc8p/MAjPEAjPEAjPELkfH1+v7///////+j1fkAjPEAjPEAjPEAi/Fpu/bu9/0LkPHg
        8f3D5PsAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEA
        jPEAjPEAjPEAjPEAjPEAjPGe0/n////////q9f0AjPEAjPEAjPEAjPEvofMnnfP/////////////////
        ///////+/v7+/v7+/v7////+/v5uvvdAqfQAi/EAi/AAi/ALkfH0+v7+/v7+/v6j1fkAjPEAi/AAjPAB
        jPHD5Puv2voAi/GQzPj7/f43pPQAi/EAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPGe0/n+/v7+/v7q9f0Ai/AAi/AAi/AAjPAyovMonvP+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvvZIrPUAjPEBjfECjfEPkvH0+v7+/v7+/v6k
        1fkAjPEAi/AAjPFXs/X8/f5Gq/QAjPEfmfLu9/3c7/wrn/MAi/AAjPEAi/EAjPAAi/AAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPGe0/n+/v7+/v7q9f0Ai/AAi/AA
        i/AAi/A2pPMpnvP+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvfZSsPVSsfVYs/VdtvZp
        u/b4/P7+/v7+/v7H5vxiuPZctfZ4wvfx+P3F5fsknPMQk/EBjPFJrPXw+P32+/6i1flgt/Y+p/QyovMx
        ovMxovMxovMxovMxovMxovMxovMxovMxovMxovMxovMxovMxovMxovMxovMxovMxovMxovOx2/r+/v7+
        /v7p9f0Ai/EAi/AAi/AAi/E6pvQqn/P+/v7////+/v7+/v7+/v7///////////////////////9uvvdU
        sfVbtfZftvZiuPZsvfb5/P7////+/v7V7Pyh1fnU6/z9/v7d7/2Ny/iLyviNzPiGyPhtvfdyv/fB4/vr
        9v37/f7+/v7+/v7+//7/////////////////////////////////////////////////////////////
        ///////+/v7+/v7////n9P0AjPEAjPEAjPEAjPE9p/Qrn/P////////////////////////+/v7+/v7+
        /v7////+/v5uvvdYs/VZtPVdtvZgt/ZrvPb5/P7+/v7+/v7+/v7+/v7u9/223vqEx/iFyPiJyfiNy/iQ
        zfiUzvmY0Pmc0vme0/md0/mLyvhmufZEqvRAqfRAqfRAqfRAqfRAqfRAqfRAqfRAqfRAqfRAqfRAqfRA
        qfRAqfRAqfRAqfRAqfRAqfRkufbz+f79/v6FyPgAjPEAi/AAi/AAjPBBqfQtoPP+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v5uvvddtvZXs/VbtfVftvZpu/b4/P7+/v7+/v7R6vyNy/h5wvd8xPeA
        xfeEx/iHyPiLyviPzPiSzviW0Pma0fme0/mh1Pml1vqo1/mg1PlrvPcxovMFjvEAjPEAi/EAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/APk/EcmPIAjPEAjPEAi/AAi/AAjPFFq/QtoPP+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvvdiuPZVsvVZtPVdtfZjuPbp9f3+/v7+/v6r
        2fpzv/d2wfd6w/d+xfeCxviFyPiJyviNy/iQzfmUz/iY0Pmc0vmf1Pmi1fqn1/mq2Pmt2vqx3Pqi1flj
        uPYbmPIAi/EAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AA
        i/AAjPFJrfQuofP+/v7////+/v7+/v7+/v7///////////////////////9uvvdmuvZTsvVXs/VbtfZf
        tvZ7w/e94fuq2Ppvvvdxv/d1wfd4wvd8xPeAxfeEx/iHyPiLyviPzPmSzviW0Pia0fme0/mh1fml1vqp
        1/ms2fqv2/qz3Pq23vu53/uMy/g8p/QEjfEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEA
        jPEAjPEAjPEAjPEAjPEAjPFNrvQwofP////////////////////////+/v7+/v7+/v7////+/v5uvfZr
        vPZSsfVVsvRZtPVdtvZgt/ZkufZouvZrvPZvvvdzv/d2wfd6w/d+xPeCxveFyPiJyfiNy/iRzfmUz/iY
        0Pmc0vmf1Pmj1fqm1/mq2Pqt2vqx2/u03fq43/u74Pq+4vuj1vpMrvUEjvEAjPEAjPAAjPEAi/AAi/AA
        i/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPFQsPUxovP+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v5uvvZvvvZQsPVTsfVXs/VbtfZetvZiuPZmufZqu/ZtvfZxv/d1wPd4wvd8xPiAxfeEx/iH
        yPiLyviPzPmSzviWz/ma0fme0/mh1fml1vqo1/mr2fqv2/qz3Pq23vq53/q94frB4/vE5Pus2fpNrvUF
        jvEAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAjPFUsvUyovP+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v5uvfd0wPdOr/RRsPVVsvVZtPVdtfZgt/ZkuPZouvZrvPZvvvdzv/d2
        wfd6w/d+xfeCxveFyPiJyviNy/iQzfmUz/mY0Pmc0vmf1Pmj1fmm1/qq2Pqt2vqx2/u13fq43/u74Pu/
        4vvD4/zF5fzI5vur2fo/qPQBjPEAjPAAi/AAjPEAi/AAi/AAi/AAjPEAi/AAi/AAi/AAi/FYs/Uzo/P+
        /v7////+/v7+/v7+/v7///////////////////////9uvvd5wvdNrvVQsPVTsvVXs/VbtfZftvZiuPZm
        ufZqu/ZtvfZxv/d1wfd4wvd8xPeAxfeEx/iHyPiLyviPzPiSzviW0Pma0fme0/mh1fml1vqp2Pqs2fqv
        2/qz3Pq23vu53/u94fvB4/vE5PvH5vzK5/vN6PyY0PknnfMAjPEAjPEAjPEAjPEAjPEAjPEAjPEAjPEA
        jPEAjPFctfU0o/P////////////////////////+/v7+/v7+/v7////+/v5uvvd+xPdLrfVOr/VRsPVV
        svVZtPVdtfZgt/ZkufZouvZrvPZvvvdzv/d2wfd6w/d+xfeCxviFyPiJyviNy/iQzfiUz/iY0Pmc0vmf
        1Pmi1fqm1/qq2Pqt2vqx3Pu13fq33vu74Pq/4vvC4/vF5fzI5vvM6PzP6fzO6Pxyv/cKkPEAi/EAi/AA
        i/AAjPEAi/AAi/AAi/AAjPFft/Y1o/P+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvveC
        xvdJrfVNrvVQsPVTsfVXs/VbtfZetvZiuPZmufZqu/ZtvfZxvvd0wPd4wvd8xPd/xfeDx/iHyPiLyviP
        zPiSzviWz/ma0fme0/mh1fmk1vqo1/mr2fqv2/qz3Pq23vu53/u94frA4/vE5PzH5vvK5/vN6PzR6vzT
        6/y54Ps9p/QAjPEAi/AAjPEAi/AAi/AAi/AAjPFjuPY2pPP+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v5uvveGyPhHrPVLrvVOr/VSsPVWsvVZtPVdtfZgt/ZkufZouvZrvPZvvvdzv/d2wfd5w/d+
        xPeCxveFx/iJyfiNy/iQzfiUz/iY0Pmc0vmf1Pmi1fmn1/mq2Pmt2vqx2/u13fq43/u74Pu/4vvC4/vF
        5fzI5vvM5/zP6fvS6vzV7PzW7PyBxvgKkPEAi/EAi/EAi/AAi/AAjPFmuvY3pPP+/v7////+/v7+/v7+
        /v7///////////////////////9uvfeLyvlFq/VJrfVNrvRQsPVTsfVXs/VbtfZetvZiuPZmufZqu/Zt
        vfZxv/d1wPd4wvd8xPiAxfeEx/iHyPiLyviOzPiSzfiWz/ma0fme0/mh1Pmk1vqo2Pqr2fqv2/qz3Pq2
        3vq53/u94fvB4/vE5PvH5vvK5/zN6PzQ6vzT6/zW7f3Z7v263/swofMAi/EAjPAAjPEAjPFqu/Y4pfT/
        ///////////////////////+/v7+/v7+/v7////+/v5uvvaPzPhEq/RHrPRLrfVOr/VRsPVVsvVZtPVd
        tfZgt/ZkufZouvZrvPZvvvdzv/d2wfd6w/h+xPeCxveFyPiJyfiNy/iQzfiUz/iY0Pmc0vmf0/mi1fmm
        1/mq2Pqt2vqx2/u13fq33/u74Pq+4vvC4/vF5fzI5vvM5/zP6fzS6vzV7PzY7fzb7v3Y7fxjuPYCjPEA
        i/AAjPFtvfc5pfT+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvvaUzvlCqvRFq/RJrfVN
        rvRQsPVTsvVXs/VbtfZftvZiuPZmufZqu/ZtvfZxvvd0wfd4wvd8xPeAxfeEx/iHyPiLyviPzPiSzviW
        0Pma0fmd0/mh1fmk1vqo1/mr2fqv2/qz3Pq23vu53/u94frA4/vE5PvH5vzK5/zN6PzQ6vzT6/zW7PzZ
        7v3c7/3f8P2Wz/gOkvEAjPFxv/c6pvT+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvvaY
        0PlBqfREqvRHrPVLrfVOr/VRsPVVsvVZtPVdtvZgt/ZkufZnuvZrvPZvvvdzv/d2wfd6w/d+xfeCxviF
        yPiJyfiNy/iQzfmUzviY0Pmc0vqf1Pmi1fmm1/mq2Pqt2vqx3Pu13fq43/u74Pu+4vvC4/vF5fzI5vvM
        5/zP6fzS6vzU7PzY7fzb7v3e7/zg8f3A4vsnnfN0wPc7pvT+/v7////+/v7+/v7+/v7/////////////
        //////////9uvvec0vk/qPRDqvRFq/RJrfVNrvVQsPVTsfVXs/VbtfZftvZiuPZmufZpu/ZtvfZxvvd0
        wPd4wvd8xPeAxfeEx/iHyPiLyviPzPiSzviWz/ma0fme0/mh1fmk1vqo1/mr2fqv2/qz3Pq23vu53/u9
        4frA4/vE5PzG5fzK5/zO6PzR6vzT6/zW7fza7v3c7/3f8P3h8f3V7Pyn1/o8p/T//v7/////////////
        ///////+/v7+/v7+/v7////+/v5uvvef1Pk9p/RBqfREqvRHrPRLrfVOr/RRsPVVsvVZtPVdtfZgt/Zj
        ufZnuvZrvPZvvvdzwPd2wfd5w/d9xPeCxveFyPiJyfiNy/iQzfiUzviY0Pmc0vmf1Pmi1fmm1/mq2Pmt
        2vqx3Pq13fq43vu74Pu/4vvC4/vF5fzI5vvM6PzP6f3S6vzV7PzY7fzb7v3e7/zg8f3i8v3X7fw/qPT+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvvej1fk7p/Q/qPRCqvRFq/RJrfVNrvVQsPVN
        r/UPk/EPk/FFq/RctfYUlPISlPJMrvVvvvc/qPQWlfINkfEYlvJHq/WEx/iLyviPzPhMrvQal/IvofOd
        0/mh1PlYtPUdmfIemfIemfIfmvInnfOJyvghmvIhm/Iim/Iim/IknPO63/vQ6vzT6/zW7f3Z7v3c7/zf
        8P3h8f3b7v0+qPT+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvven1/o6pvQ9p/RAqfNE
        qvRHrPVLrfVOr/VKrfUAjPEAi/A+qPRZtPUCjfEAi/BDqvQ0o/MAjPEEjfEpnvMFjvEAjPE8p/SJyviN
        y/g9p/QAi/AYl/Kc0vqf0/lHrPQAi/ALkPE1pPQ2pPM9p/R/xfgAjPEAi/ADjfEDjfEEjfG13frP6fzS
        6vzU7PzY7f3b7v3e7/3f8f3e7/0/qPT+/v7////+/v7+/v7+/v7///////////////////////9uvveq
        2Po4pfQ7p/Q/qPRCqvRFrPRJrfVNrvVJrPUAjPEAi/EcmPIonvMBjPEAi/FBqfQMkfEAjPErn/N4wvcx
        ovMAjPEKkPGGyPiLyvg8p/QAjPEYlvKa0fme0/lHrPQAjPETlPJgt/ZiuPZ2wfeBxvgAjPEAjPGb0vm1
        3fu43/vI5vzN6PzQ6vvT6/zW7fzZ7v3c7/3f8P3g8P0/qPT////////////////////////+/v7+/v7+
        /v7////+/v5uvfeu2vo3pPQ6pvQ9p/RBqfNEqvRHrPVLrfRIrPQAjPEAjPAAi/EAi/EAjPAAi/A+p/QF
        jvEAjPE0o/N2wfc6pvQAi/EBjPGDx/iIyfg7pvQAjPAYlvKY0Pmb0vlGq/QAi/AAi/EAi/EAi/EonvOA
        xfgAi/EAjPCl1/nC4/vF5fvI5vvM6PzP6fzS6vzU7PzY7fzb7v3d7/zi8f1AqfT+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v5uvfex2/o1pPQ4pfM7pvQ/qPRCqvRFq/RJrfVGq/QAjPEAjPAnnfI4
        pfQBjPEAi/A/qPQTlPEAi/AdmfJxv/chmvIAjPEUlPJgt/ZUsvUlnPMAjPEOkvFdtvZjuPZDqvQAi/Ad
        mfKY0Pmb0fmh1Pl+xfgAi/EAjPCk1fnA4/vE5PzG5fzK5/zN6PzQ6vzT6/vW7PzZ7v3c7/3k8v1BqfT+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v5uvfez3fozo/Q3pPM6pvQ9p/RBqfREqvRHrPRE
        qvUAjPEAi/A5pfNSsfUCjfEAi/A+qPRIrPQDjfEAjPAFjvEAi/EEjfFVsvUmnfMAi/AAjPEAi/AAi/AA
        i/AIj/E/qPQAi/AAi/AAjPEAi/ARk/J7w/gAjPAAi/Ci1fm/4vrC4/vF5fzI5vvM5/zP6fzS6vzU7PzY
        7fza7v3l8/1CqfT+/v7////+/v7+/v7+/v7////////////////////+/v95wvey3Po1o/Q1pPQ4pfM7
        pvQ/qPRCqvRFq/RGq/UmnfMnnvNFq/RTsfUuoPMvofNPr/VlufZXs/U2pPMuofM7pvRjuPZ7xPdSsfVB
        qfRDqvREq/RGrPRIrPROr/VsvPZOr/VPsPVRsPVTsfVetvaUzvlZtPVatPat2vu94frA4/vE5PzH5fzK
        5/vN6PzQ6vvT6/zW7f3Y7f3p9f1BqfT+/v7////////////////////+/v7+/v7+/v7////+/v6q2PmJ
        yfhctfYzo/M3pPM6pvM9p/RAqfNEqvRHrPVKrvVOr/VRsfVVsvVZtPVdtfZgt/ZjuPZnuvZrvPZvvvdz
        wPd2wfd6w/d9xPeBxveFyPiJyfiNy/iQzfmUzviX0Pmb0vmf0/mi1fqm1/mq2Pqt2vqx3Pq13fq33vu7
        4Pu+4vvC4/vF5fzI5vvL6PzP6fzS6vzV7PzY7fzV7PxatPX+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7x+P0+p/S53/s0o/M1o/Q4pfM7pvQ+qPRCqvRFq/RJrfVMrvRQsPVTsfVXs/VbtfVetvZh
        uPZlufZpu/ZtvfZwvvd0wPd3wvd7xPd/xfeEx/iHyPiLyviOzPiSzviWz/ma0fmd0/qh1Pml1vqo1/mr
        2fqv2/qz3Pq23vu53/u94fvA4/vE5PvG5fzK5/zN6PzQ6vvT6/zk8v11wPe03fr+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v6f0/ltvfaq2Po7pvQ3pPM6pvQ9p/RAqfNEqvRHrPRKrfVPr/VR
        sPVVsvVZtPVdtfZgt/ZkuPZouvZrvPZvvvdzv/d2wfd5w/d9xPeCxveFyPiJyfiNy/iQzfiUz/iY0Pmb
        0vmf1Pmi1fqm1/mq2Pqt2vqx3Pu03fu43/u74Pu+4fvD4/vF5fzI5vvL6PzP6fzf8P223vpVsvX9/v7+
        /v7////+/v7+/v7+/v7////////////////////////////9/v6FyPhatPXG5fua0fl+xfd9xPeAxviC
        xveFx/iHyfiJyviLy/iNzPmQzfiSzviUz/mX0Pia0fmc0vme0/mh1fqj1vql1/mo2Pqr2fqt2vqw2/qy
        3Pq03fq33vu53/q74fu+4vvB4/vD5PvF5fvH5vvK5/zM6PzO6fzR6vzT6/zV7PzX7fzZ7vzg8f3n9P2L
        y/hNrvXv+P3////////////////////////////+/v7+/v7+/v7////+/v7+/v7+/v7+/v7E5PtQsPVI
        rPVjuPZqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zq
        u/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zqu/Zq
        u/ZnuvZSsfVCqfSZ0Pn5/P7+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v70+v7d7/3a7v3a7vza7vza7v3a7v3a7v3a7v3a7v3a7v3a7v3a7v3a7v3a7vza7v3a
        7v3a7v3a7vza7vza7v3a7vza7v3a7v3a7v3a7v3a7vza7vza7v3a7v3a7v3a7v3a7v3a7v3a7v3a7vza
        7vza7v3a7vza7v3a7v3b7v3s9v39/v7+/v7+/v7+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7/////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ///////////////////////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+
        /v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v7////+/v7+/v7+/v4AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==
</value>
  </data>
</root>