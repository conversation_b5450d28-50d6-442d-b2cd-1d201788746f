﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="SIS_DBDataSet3" targetNamespace="http://tempuri.org/SIS_DBDataSet3.xsd" xmlns:mstns="http://tempuri.org/SIS_DBDataSet3.xsd" xmlns="http://tempuri.org/SIS_DBDataSet3.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="SIS_DBConnectionString1" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="SIS_DBConnectionString1 (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Sales_and_Inventory_System.My.MySettings.GlobalReference.Default.SIS_DBConnectionString1" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CompanyTableAdapter" GeneratorDataComponentClassName="CompanyTableAdapter" Name="Company" UserDataComponentName="CompanyTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Company" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Company] WHERE (([ID] = @Original_ID) AND ([CompanyName] = @Original_CompanyName) AND ([Address] = @Original_Address) AND ([ContactNo] = @Original_ContactNo) AND ([EmailID] = @Original_EmailID) AND ((@IsNull_TIN = 1 AND [TIN] IS NULL) OR ([TIN] = @Original_TIN)) AND ((@IsNull_STNo = 1 AND [STNo] IS NULL) OR ([STNo] = @Original_STNo)) AND ((@IsNull_CIN = 1 AND [CIN] IS NULL) OR ([CIN] = @Original_CIN)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CompanyName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CompanyName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_TIN" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_TIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_STNo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_STNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CIN" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Company] ([CompanyName], [Address], [ContactNo], [EmailID], [Logo], [TIN], [STNo], [CIN]) VALUES (@CompanyName, @Address, @ContactNo, @EmailID, @Logo, @TIN, @STNo, @CIN);
SELECT ID, CompanyName, Address, ContactNo, EmailID, Logo, TIN, STNo, CIN FROM Company WHERE (ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CompanyName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CompanyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="@Logo" Precision="0" ProviderType="Image" Scale="0" Size="0" SourceColumn="Logo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@TIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@STNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ID, CompanyName, Address, ContactNo, EmailID, Logo, TIN, STNo, CIN FROM dbo.Company</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Company] SET [CompanyName] = @CompanyName, [Address] = @Address, [ContactNo] = @ContactNo, [EmailID] = @EmailID, [Logo] = @Logo, [TIN] = @TIN, [STNo] = @STNo, [CIN] = @CIN WHERE (([ID] = @Original_ID) AND ([CompanyName] = @Original_CompanyName) AND ([Address] = @Original_Address) AND ([ContactNo] = @Original_ContactNo) AND ([EmailID] = @Original_EmailID) AND ((@IsNull_TIN = 1 AND [TIN] IS NULL) OR ([TIN] = @Original_TIN)) AND ((@IsNull_STNo = 1 AND [STNo] IS NULL) OR ([STNo] = @Original_STNo)) AND ((@IsNull_CIN = 1 AND [CIN] IS NULL) OR ([CIN] = @Original_CIN)));
SELECT ID, CompanyName, Address, ContactNo, EmailID, Logo, TIN, STNo, CIN FROM Company WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CompanyName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CompanyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="@Logo" Precision="0" ProviderType="Image" Scale="0" Size="0" SourceColumn="Logo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@TIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@STNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CompanyName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CompanyName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_TIN" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_TIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_STNo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_STNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CIN" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID" ColumnName="ID" DataSourceName="INV_DB.dbo.Company" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="CompanyName" DataSetColumn="CompanyName" />
              <Mapping SourceColumn="Address" DataSetColumn="Address" />
              <Mapping SourceColumn="ContactNo" DataSetColumn="ContactNo" />
              <Mapping SourceColumn="EmailID" DataSetColumn="EmailID" />
              <Mapping SourceColumn="Logo" DataSetColumn="Logo" />
              <Mapping SourceColumn="TIN" DataSetColumn="TIN" />
              <Mapping SourceColumn="STNo" DataSetColumn="STNo" />
              <Mapping SourceColumn="CIN" DataSetColumn="CIN" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="SIS_DBDataSet3" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="True" msprop:Generator_DataSetName="SIS_DBDataSet3" msprop:Generator_UserDSName="SIS_DBDataSet3">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Company" msprop:Generator_TableClassName="CompanyDataTable" msprop:Generator_TableVarName="tableCompany" msprop:Generator_TablePropName="Company" msprop:Generator_RowDeletingName="CompanyRowDeleting" msprop:Generator_RowChangingName="CompanyRowChanging" msprop:Generator_RowEvHandlerName="CompanyRowChangeEventHandler" msprop:Generator_RowDeletedName="CompanyRowDeleted" msprop:Generator_UserTableName="Company" msprop:Generator_RowChangedName="CompanyRowChanged" msprop:Generator_RowEvArgName="CompanyRowChangeEvent" msprop:Generator_RowClassName="CompanyRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="CompanyName" msprop:Generator_ColumnVarNameInTable="columnCompanyName" msprop:Generator_ColumnPropNameInRow="CompanyName" msprop:Generator_ColumnPropNameInTable="CompanyNameColumn" msprop:Generator_UserColumnName="CompanyName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnPropNameInTable="AddressColumn" msprop:Generator_UserColumnName="Address">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" msprop:Generator_UserColumnName="ContactNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EmailID" msprop:Generator_ColumnVarNameInTable="columnEmailID" msprop:Generator_ColumnPropNameInRow="EmailID" msprop:Generator_ColumnPropNameInTable="EmailIDColumn" msprop:Generator_UserColumnName="EmailID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Logo" msprop:Generator_ColumnVarNameInTable="columnLogo" msprop:Generator_ColumnPropNameInRow="Logo" msprop:Generator_ColumnPropNameInTable="LogoColumn" msprop:Generator_UserColumnName="Logo" type="xs:base64Binary" />
              <xs:element name="TIN" msprop:Generator_ColumnVarNameInTable="columnTIN" msprop:Generator_ColumnPropNameInRow="TIN" msprop:Generator_ColumnPropNameInTable="TINColumn" msprop:Generator_UserColumnName="TIN" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="STNo" msprop:Generator_ColumnVarNameInTable="columnSTNo" msprop:Generator_ColumnPropNameInRow="STNo" msprop:Generator_ColumnPropNameInTable="STNoColumn" msprop:Generator_UserColumnName="STNo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CIN" msprop:Generator_ColumnVarNameInTable="columnCIN" msprop:Generator_ColumnPropNameInRow="CIN" msprop:Generator_ColumnPropNameInTable="CINColumn" msprop:Generator_UserColumnName="CIN" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Company" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
  </xs:element>
</xs:schema>