Warning	1	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	2	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	3	The primary reference "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	4	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	5	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	6	The primary reference "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	7	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	8	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	9	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	10	Could not resolve this reference. Could not locate the assembly "TouchlessLib". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.	Sales and Inventory System
Warning	11	Cannot get the file path for type library "237f4bec-8ae5-41e1-ae84-b194e4670597" version 13.0. Library not registered. (Exception from HRESULT: 0x8002801D (TYPE_E_LIBNOTREGISTERED))	Sales and Inventory System
Error	12	The tablix 'ProductTable' has an incorrect number of TablixCells. The number of TablixCells in a TablixRow must equal the number of innermost TablixMembers (TablixMembers with no submembers) in the TablixColumnHierarchy.	D:\b Sales and Inventory System\Reports\InvoiceReport.rdlc	Sales and Inventory System
Error	13	The tablix 'ProductTable' has an invalid set of TablixCells. The combined value of the ColSpan properties of the TablixCells within a TablixRow does not equal the number of leaf TablixMembers (TablixMembers with no submembers) in the TablixColumnHierarchy.	D:\b Sales and Inventory System\Reports\InvoiceReport.rdlc	Sales and Inventory System
Error	14	The tablix 'SalesTable' has an incorrect number of TablixCells. The number of TablixCells in a TablixRow must equal the number of innermost TablixMembers (TablixMembers with no submembers) in the TablixColumnHierarchy.	D:\b Sales and Inventory System\Reports\SalesReport.rdlc	Sales and Inventory System
Error	15	The tablix 'SalesTable' has an invalid set of TablixCells. The combined value of the ColSpan properties of the TablixCells within a TablixRow does not equal the number of leaf TablixMembers (TablixMembers with no submembers) in the TablixColumnHierarchy.	D:\b Sales and Inventory System\Reports\SalesReport.rdlc	Sales and Inventory System
Error	16	The tablix 'StockTable' has an incorrect number of TablixCells. The number of TablixCells in a TablixRow must equal the number of innermost TablixMembers (TablixMembers with no submembers) in the TablixColumnHierarchy.	D:\b Sales and Inventory System\Reports\StockInReport.rdlc	Sales and Inventory System
Error	17	The tablix 'StockTable' has an invalid set of TablixCells. The combined value of the ColSpan properties of the TablixCells within a TablixRow does not equal the number of leaf TablixMembers (TablixMembers with no submembers) in the TablixColumnHierarchy.	D:\b Sales and Inventory System\Reports\StockInReport.rdlc	Sales and Inventory System
Error	18	The tablix 'CustomerTable' has an incorrect number of TablixCells. The number of TablixCells in a TablixRow must equal the number of innermost TablixMembers (TablixMembers with no submembers) in the TablixColumnHierarchy.	D:\b Sales and Inventory System\Reports\CustomerReport.rdlc	Sales and Inventory System
Error	19	The tablix 'CustomerTable' has an invalid set of TablixCells. The combined value of the ColSpan properties of the TablixCells within a TablixRow does not equal the number of leaf TablixMembers (TablixMembers with no submembers) in the TablixColumnHierarchy.	D:\b Sales and Inventory System\Reports\CustomerReport.rdlc	Sales and Inventory System
Warning	20	The referenced component 'TouchlessLib' could not be found. 	Sales and Inventory System
Warning	21	The referenced component 'PAGEOBJECTMODELLib' could not be found. 	Sales and Inventory System
Warning	22	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptBarcodeLabelPrinting.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	23	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditors.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	24	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTerms.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	25	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTermsStatements.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	26	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTermsStatementsByCustomer.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	27	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCustomerLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	28	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptDebtors.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	29	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptExpenses.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	30	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptGeneralDayBook.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	31	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptGeneralLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	32	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptInvoice.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	33	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptInvoice1.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	34	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptInvoice2.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	35	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptInvoiceReturn.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	36	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptOverall.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	37	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptPurchase.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	38	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptPurchaseDayBook.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	39	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptQuotation.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	40	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptQuotation1.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	41	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	42	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales1.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	43	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales2.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	44	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesmanCommission.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	45	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesmanLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	46	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesmanLedger_2.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	47	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesTaxReport.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	48	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptService.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	49	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptServiceReceipt.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	50	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptServiceTaxReport.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	51	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptStockIn.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	52	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptStockOut.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	53	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSupplierLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	54	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptTrialBalance.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	55	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptVoucher.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	56	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTexpenses.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	57	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTpurchases.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	58	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTsales.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	59	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTservice.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Error	60	Unable to open module file 'D:\b Sales and Inventory System\ReportManager.vb': System Error &H80070020&	D:\b Sales and Inventory System\ReportManager.vb	1	1	Sales and Inventory System
Error	61	Unable to open module file 'D:\b Sales and Inventory System\SubRPTexpenses.vb': System Error &H80070002&	D:\b Sales and Inventory System\SubRPTexpenses.vb	1	1	Sales and Inventory System
Error	62	Unable to open module file 'D:\b Sales and Inventory System\SubRPTsales.vb': System Error &H80070002&	D:\b Sales and Inventory System\SubRPTsales.vb	1	1	Sales and Inventory System
Error	63	'Picsart_23_03_19_11_27_15_052' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmAbout.Designer.vb	73	35	Sales and Inventory System
Error	64	'Activate' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmActivation.Designer.vb	159	28	Sales and Inventory System
Error	65	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmChangePassword.Designer.vb	186	30	Sales and Inventory System
Error	66	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmChangePassword.Designer.vb	203	32	Sales and Inventory System
Error	67	'Company1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmCompany.Designer.vb	140	32	Sales and Inventory System
Error	68	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmCreditTermsStatementsReport.vb	86	13	Sales and Inventory System
Error	69	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmCreditTermsStatementsReport.vb	156	13	Sales and Inventory System
Error	70	'photo' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmCustomer.Designer.vb	247	28	Sales and Inventory System
Error	71	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmCustomer.vb	26	25	Sales and Inventory System
Error	72	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmCustomer.vb	380	25	Sales and Inventory System
Error	73	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmCustomerLedger.vb	90	13	Sales and Inventory System
Error	74	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmDebtorsReport.vb	35	13	Sales and Inventory System
Error	75	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmDebtorsReport.vb	54	13	Sales and Inventory System
Error	76	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmDebtorsReport.vb	68	13	Sales and Inventory System
Error	77	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmGeneralDayBook.vb	40	13	Sales and Inventory System
Error	78	'Picsart_23_03_19_11_27_15_0522' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	152	35	Sales and Inventory System
Error	79	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	203	32	Sales and Inventory System
Error	80	'panelControl1_ContentImage' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	241	30	Sales and Inventory System
Error	81	'Reset2_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	107	29	Sales and Inventory System
Error	82	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	217	29	Sales and Inventory System
Error	83	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	240	37	Sales and Inventory System
Error	84	'Billing_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	173	45	Sales and Inventory System
Error	85	'basket_full_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	220	45	Sales and Inventory System
Error	86	'payment_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	229	39	Sales and Inventory System
Error	87	'edit_file_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	247	45	Sales and Inventory System
Error	88	'Stocks_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	256	45	Sales and Inventory System
Error	89	'User_Group_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	265	46	Sales and Inventory System
Error	90	'Users_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	274	47	Sales and Inventory System
Error	91	'Admin_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	283	46	Sales and Inventory System
Error	92	'Utilities_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	293	45	Sales and Inventory System
Error	93	'Inventory_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	314	47	Sales and Inventory System
Error	94	'messages_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	350	45	Sales and Inventory System
Error	95	'product_sales_report_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	360	44	Sales and Inventory System
Error	96	'report_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	430	45	Sales and Inventory System
Error	97	'Database_Active_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	561	46	Sales and Inventory System
Error	98	'log_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	582	42	Sales and Inventory System
Error	99	'Actions_user_group_new_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	591	50	Sales and Inventory System
Error	100	'Log_Out_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	609	39	Sales and Inventory System
Error	101	'basket_full_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	673	46	Sales and Inventory System
Error	102	'Entypo_d83d_0__512' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	681	49	Sales and Inventory System
Error	103	'Excel_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	865	35	Sales and Inventory System
Error	104	'Reset2_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	881	29	Sales and Inventory System
Error	105	'keyboard_icon__1_' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1604	32	Sales and Inventory System
Error	106	'User_Interface_Restore_Window_icon__1_' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1622	32	Sales and Inventory System
Error	107	'Programming_Minimize_Window_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1640	32	Sales and Inventory System
Error	108	'Button_Delete_icon11' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1657	29	Sales and Inventory System
Error	109	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment.Designer.vb	167	29	Sales and Inventory System
Error	110	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment_2.Designer.vb	283	29	Sales and Inventory System
Error	111	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment_3.Designer.vb	277	29	Sales and Inventory System
Error	112	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmProfitAndLossReport.vb	48	13	Sales and Inventory System
Error	113	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmPurchaseDaybook.vb	41	13	Sales and Inventory System
Error	114	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmPurchaseReport.vb	30	13	Sales and Inventory System
Error	115	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmPurchaseReport.vb	45	13	Sales and Inventory System
Error	116	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPurchaseReturn.Designer.vb	675	29	Sales and Inventory System
Error	117	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmRecoveryPassword.Designer.vb	111	30	Sales and Inventory System
Error	118	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmRecoveryPassword.Designer.vb	129	32	Sales and Inventory System
Error	119	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSalesReport.vb	49	13	Sales and Inventory System
Error	120	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSalesReport.vb	79	13	Sales and Inventory System
Error	121	'product_sales_report_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSalesReturn.Designer.vb	1048	29	Sales and Inventory System
Error	122	Type 'rptInvoiceReturn' is not defined.	D:\b Sales and Inventory System\frmSalesReturn.vb	89	28	Sales and Inventory System
Error	123	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSalesReturn.vb	111	13	Sales and Inventory System
Error	124	'photo' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSalesman.Designer.vb	202	28	Sales and Inventory System
Error	125	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmSalesman.vb	23	25	Sales and Inventory System
Error	126	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmSalesman.vb	304	25	Sales and Inventory System
Error	127	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSalesmanCommissionReport.vb	55	13	Sales and Inventory System
Error	128	'Database_Active_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSqlServerSetting.Designer.vb	88	38	Sales and Inventory System
Error	129	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmStockInAndOutReport.vb	18	13	Sales and Inventory System
Error	130	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmStockInAndOutReport.vb	27	13	Sales and Inventory System
Error	131	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSupplier.Designer.vb	121	29	Sales and Inventory System
Error	132	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSupplierLedger.vb	82	13	Sales and Inventory System
Error	133	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmTaxReport.vb	57	13	Sales and Inventory System
Error	134	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmTaxReport.vb	92	13	Sales and Inventory System
Error	135	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmTrialBalance.vb	57	13	Sales and Inventory System
Error	136	Unable to open module file 'D:\b Sales and Inventory System\rptBarcodeLabelPrinting.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptBarcodeLabelPrinting.vb	1	1	Sales and Inventory System
Error	137	Unable to open module file 'D:\b Sales and Inventory System\rptCreditTermsStatementsByCustomer.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptCreditTermsStatementsByCustomer.vb	1	1	Sales and Inventory System
Error	138	Unable to open module file 'D:\b Sales and Inventory System\rptCustomerLedger.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptCustomerLedger.vb	1	1	Sales and Inventory System
Error	139	Unable to open module file 'D:\b Sales and Inventory System\rptDebtors.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptDebtors.vb	1	1	Sales and Inventory System
Error	140	Unable to open module file 'D:\b Sales and Inventory System\rptExpenses.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptExpenses.vb	1	1	Sales and Inventory System
Error	141	Unable to open module file 'D:\b Sales and Inventory System\rptGeneralDayBook.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptGeneralDayBook.vb	1	1	Sales and Inventory System
Error	142	Unable to open module file 'D:\b Sales and Inventory System\rptGeneralLedger.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptGeneralLedger.vb	1	1	Sales and Inventory System
Error	143	Unable to open module file 'D:\b Sales and Inventory System\rptInvoice.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptInvoice.vb	1	1	Sales and Inventory System
Error	144	Unable to open module file 'D:\b Sales and Inventory System\rptInvoice1.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptInvoice1.vb	1	1	Sales and Inventory System
Error	145	Unable to open module file 'D:\b Sales and Inventory System\rptInvoice2.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptInvoice2.vb	1	1	Sales and Inventory System
Error	146	Unable to open module file 'D:\b Sales and Inventory System\rptInvoiceReturn.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptInvoiceReturn.vb	1	1	Sales and Inventory System
Error	147	Unable to open module file 'D:\b Sales and Inventory System\rptOverall.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptOverall.vb	1	1	Sales and Inventory System
Error	148	Unable to open module file 'D:\b Sales and Inventory System\rptPurchase.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptPurchase.vb	1	1	Sales and Inventory System
Error	149	Unable to open module file 'D:\b Sales and Inventory System\rptPurchaseDayBook.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptPurchaseDayBook.vb	1	1	Sales and Inventory System
Error	150	Unable to open module file 'D:\b Sales and Inventory System\rptQuotation.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptQuotation.vb	1	1	Sales and Inventory System
Error	151	Unable to open module file 'D:\b Sales and Inventory System\rptQuotation1.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptQuotation1.vb	1	1	Sales and Inventory System
Error	152	Unable to open module file 'D:\b Sales and Inventory System\rptSalesTaxReport.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptSalesTaxReport.vb	1	1	Sales and Inventory System
Error	153	Unable to open module file 'D:\b Sales and Inventory System\rptSalesmanCommission.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptSalesmanCommission.vb	1	1	Sales and Inventory System
Error	154	Unable to open module file 'D:\b Sales and Inventory System\rptSalesmanLedger_2.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptSalesmanLedger_2.vb	1	1	Sales and Inventory System
Error	155	Unable to open module file 'D:\b Sales and Inventory System\rptServiceReceipt.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptServiceReceipt.vb	1	1	Sales and Inventory System
Error	156	Unable to open module file 'D:\b Sales and Inventory System\rptServiceTaxReport.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptServiceTaxReport.vb	1	1	Sales and Inventory System
Error	157	Unable to open module file 'D:\b Sales and Inventory System\rptStockIn.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptStockIn.vb	1	1	Sales and Inventory System
Error	158	Unable to open module file 'D:\b Sales and Inventory System\rptStockOut.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptStockOut.vb	1	1	Sales and Inventory System
Error	159	Unable to open module file 'D:\b Sales and Inventory System\rptSupplierLedger.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptSupplierLedger.vb	1	1	Sales and Inventory System
Error	160	Unable to open module file 'D:\b Sales and Inventory System\rptVoucher.vb': System Error &H80070002&	D:\b Sales and Inventory System\rptVoucher.vb	1	1	Sales and Inventory System
Error	161	Maximum number of errors has been exceeded.	Sales and Inventory System
