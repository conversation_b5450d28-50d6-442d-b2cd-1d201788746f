﻿
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual Studio 2010
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "Sales and Inventory System", "Sales and Inventory System.vbproj", "{0FBE5890-9982-4357-AD91-054D44D762CE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|Mixed Platforms = Release|Mixed Platforms
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Debug|Any CPU.ActiveCfg = Debug|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Debug|x86.ActiveCfg = Debug|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Debug|x86.Build.0 = Debug|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Release|Any CPU.ActiveCfg = Release|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Release|Mixed Platforms.Build.0 = Release|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Release|x86.ActiveCfg = Release|x86
		{0FBE5890-9982-4357-AD91-054D44D762CE}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
