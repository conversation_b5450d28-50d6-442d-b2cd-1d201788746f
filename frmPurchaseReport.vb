﻿Imports System.Data.SqlClient

Imports System.IO

Public Class frmPurchaseReport
    Dim a, b, c As Decimal

    Sub Reset()
        cmbSupplier.Text = ""
        dtpDateFrom.Value = Today
        dtpDateTo.Value = Today
    End Sub
    Private Sub btnReset_Click(sender As System.Object, e As System.EventArgs) Handles btnReset.Click
        Reset()
    End Sub

    Private Sub btnClose_Click(sender As System.Object, e As System.EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub btnViewReport_Click(sender As System.Object, e As System.EventArgs) Handles btnViewReport.Click
        Try
            If Len(Trim(cmbSupplier.Text)) = 0 Then
                MessageBox.Show("الرجاء اختيار المورد", "", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                cmbSupplier.Focus()
                Exit Sub
            End If

            ' استخدام النظام الجديد للتقارير - تقرير المشتريات حسب المورد
            ReportManager.ShowSupplierPurchaseReport(cmbSupplier.Text, dtpDateFrom.Value.Date, dtpDateTo.Value.Date)

        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub Timer1_Tick(sender As System.Object, e As System.EventArgs) Handles Timer1.Tick
        Cursor = Cursors.Default
        Timer1.Enabled = False
    End Sub

    Private Sub btnGetData_Click(sender As System.Object, e As System.EventArgs) Handles btnGetData.Click
        Try
            ' استخدام النظام الجديد للتقارير - تقرير المشتريات حسب التاريخ
            ReportManager.ShowPurchaseReport(dtpDateFrom.Value.Date, dtpDateTo.Value.Date)

        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Sub fillSupplier()
        Try
            con = New SqlConnection(cs)
            con.Open()
            adp = New SqlDataAdapter()
            adp.SelectCommand = New SqlCommand("SELECT RTRIM(Name) FROM Supplier order by Name", con)
            ds = New DataSet("ds")
            adp.Fill(ds)
            dtable = ds.Tables(0)
            cmbSupplier.Items.Clear()
            For Each drow As DataRow In dtable.Rows
                cmbSupplier.Items.Add(drow(0).ToString())
            Next
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    Private Sub frmPurchaseReport_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        fillSupplier()
    End Sub

    Private Sub cmbCompany_Format(sender As System.Object, e As System.Windows.Forms.ListControlConvertEventArgs) Handles cmbSupplier.Format
        If (e.DesiredType Is GetType(String)) Then
            e.Value = e.Value.ToString.Trim
        End If
    End Sub
End Class
