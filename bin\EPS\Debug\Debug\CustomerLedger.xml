<?xml version="1.0" standalone="yes"?>
<xs:schema id="NewDataSet" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="NewDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Table1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Date" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Name" type="xs:string" minOccurs="0" />
              <xs:element name="LedgerNo" type="xs:string" minOccurs="0" />
              <xs:element name="Label" type="xs:string" minOccurs="0" />
              <xs:element name="Credit" type="xs:double" minOccurs="0" />
              <xs:element name="Debit" type="xs:double" minOccurs="0" />
              <xs:element name="Manual_Inv" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Table2">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" type="xs:int" minOccurs="0" />
              <xs:element name="CompanyName" type="xs:string" minOccurs="0" />
              <xs:element name="Address" type="xs:string" minOccurs="0" />
              <xs:element name="ContactNo" type="xs:string" minOccurs="0" />
              <xs:element name="EmailID" type="xs:string" minOccurs="0" />
              <xs:element name="Logo" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="TIN" type="xs:string" minOccurs="0" />
              <xs:element name="STNo" type="xs:string" minOccurs="0" />
              <xs:element name="CIN" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>