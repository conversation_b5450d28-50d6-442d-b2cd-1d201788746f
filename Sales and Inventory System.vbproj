﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{0FBE5890-9982-4357-AD91-054D44D762CE}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>Sales_and_Inventory_System.My.MyApplication</StartupObject>
    <RootNamespace>Sales_and_Inventory_System</RootNamespace>
    <AssemblyName>One House ERP</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>C:\Users\<USER>\Desktop\Inventory Software v4.0.1.0 - Arabic\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>One House ERP.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>One House ERP.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Picsart_23-03-19_11-27-15-052.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>D9C9BD3E065A4466EC76DB86745149B18B1DB0DE</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>Sales and Inventory System_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.ProcessingObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="FlashControlV71, Version=1.0.3187.32366, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ConnectionInfo, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.ConnectionInfo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ConnectionInfoExtended, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.ConnectionInfoExtended.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.Sdk.Sfc, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.Management.Sdk.Sfc.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Microsoft.SqlServer.Smo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Vbe.Interop, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="TouchlessLib">
      <HintPath>..\Hotel Management System\bin\Debug\TouchlessLib.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Application.vb" />
    <Compile Include="ApplicationEvents.vb" />
    <Compile Include="DataSet1.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </Compile>
    <Compile Include="frmSalesLocations.Designer.vb">
      <DependentUpon>frmSalesLocations.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesLocations.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmContacts_2.Designer.vb">
      <DependentUpon>frmContacts_2.vb</DependentUpon>
    </Compile>
    <Compile Include="frmContacts_2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerRecord6.Designer.vb">
      <DependentUpon>frmCustomerRecord6.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerRecord6.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerRecord5.Designer.vb">
      <DependentUpon>frmCustomerRecord5.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerRecord5.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerRecord4.Designer.vb">
      <DependentUpon>frmCustomerRecord4.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerRecord4.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerRecord3.Designer.vb">
      <DependentUpon>frmCustomerRecord3.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerRecord3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPaymentRecord_3.Designer.vb">
      <DependentUpon>frmPaymentRecord_3.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPaymentRecord_3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPaymentRecord_2.Designer.vb">
      <DependentUpon>frmPaymentRecord_2.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPaymentRecord_2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPayment_3.Designer.vb">
      <DependentUpon>frmPayment_3.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPayment_3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPayment_2.Designer.vb">
      <DependentUpon>frmPayment_2.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPayment_2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesmanRecord_3.Designer.vb">
      <DependentUpon>frmSalesmanRecord_3.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesmanRecord_3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesmanRecord_2.Designer.vb">
      <DependentUpon>frmSalesmanRecord_2.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesmanRecord_2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesReturnRecord.Designer.vb">
      <DependentUpon>frmSalesReturnRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesReturnRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesReturn.Designer.vb">
      <DependentUpon>frmSalesReturn.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesReturn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmBarcodeLabelPrinting.Designer.vb">
      <DependentUpon>frmBarcodeLabelPrinting.vb</DependentUpon>
    </Compile>
    <Compile Include="frmBarcodeLabelPrinting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomDialog14.Designer.vb">
      <DependentUpon>frmCustomDialog14.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomDialog14.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomDialog15.Designer.vb">
      <DependentUpon>frmCustomDialog15.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomDialog15.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomDialog5.Designer.vb">
      <DependentUpon>frmCustomDialog5.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomDialog5.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmEmailSetting.Designer.vb">
      <DependentUpon>frmEmailSetting.vb</DependentUpon>
    </Compile>
    <Compile Include="frmEmailSetting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPurchaseReturn.Designer.vb">
      <DependentUpon>frmPurchaseReturn.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPurchaseReturn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPurchaseReturnRecord.Designer.vb">
      <DependentUpon>frmPurchaseReturnRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPurchaseReturnRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmRecoveryPassword.Designer.vb">
      <DependentUpon>frmRecoveryPassword.vb</DependentUpon>
    </Compile>
    <Compile Include="frmRecoveryPassword.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewDataSet.vb">
      <DependentUpon>NewDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="rptBarcodeLabelPrinting.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptBarcodeLabelPrinting.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptCreditTermsStatements.vb">
      <DependentUpon>rptCreditTermsStatements.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptCreditTermsStatementsByCustomer.vb">
      <DependentUpon>rptCreditTermsStatementsByCustomer.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmCreditTermsStatementsReport.Designer.vb">
      <DependentUpon>frmCreditTermsStatementsReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCreditTermsStatementsReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptCreditTerms.vb">
      <DependentUpon>rptCreditTerms.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmCreditTermsReport.Designer.vb">
      <DependentUpon>frmCreditTermsReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCreditTermsReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSendSMS.Designer.vb">
      <DependentUpon>frmSendSMS.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSendSMS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptInvoiceReturn.vb">
      <DependentUpon>rptInvoiceReturn.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptSalesmanLedger_2.vb">
      <DependentUpon>rptSalesmanLedger_2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptServiceTaxReport.vb">
      <DependentUpon>rptServiceTaxReport.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmTaxReport.Designer.vb">
      <DependentUpon>frmTaxReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTaxReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesmanCommissionReport.Designer.vb">
      <DependentUpon>frmSalesmanCommissionReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesmanCommissionReport.vb" />
    <Compile Include="frmSalesmanLedger.Designer.vb">
      <DependentUpon>frmSalesmanLedger.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesmanLedger.vb" />
    <Compile Include="frmSalesmanRecord.Designer.vb">
      <DependentUpon>frmSalesmanRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesmanRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesman.Designer.vb">
      <DependentUpon>frmSalesman.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesman.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSMS.Designer.vb">
      <DependentUpon>frmSMS.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSMS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptCustomerLedger.vb">
      <DependentUpon>rptCustomerLedger.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmCustomerLedger.Designer.vb">
      <DependentUpon>frmCustomerLedger.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerLedger.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptSalesmanCommission.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalesmanCommission.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptSalesmanLedger.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalesmanLedger.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptSalesTaxReport.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSalesTaxReport.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptSupplierLedger.vb">
      <DependentUpon>rptSupplierLedger.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmSupplierLedger.Designer.vb">
      <DependentUpon>frmSupplierLedger.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSupplierLedger.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NewDataSet.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>NewDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="rptTrialBalance.vb">
      <DependentUpon>rptTrialBalance.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmTrialBalance.Designer.vb">
      <DependentUpon>frmTrialBalance.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTrialBalance.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPaymentRecord.Designer.vb">
      <DependentUpon>frmPaymentRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPaymentRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPayment.Designer.vb">
      <DependentUpon>frmPayment.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPayment.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmGeneralDayBook.Designer.vb">
      <DependentUpon>frmGeneralDayBook.vb</DependentUpon>
    </Compile>
    <Compile Include="frmGeneralDayBook.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptGeneralDayBook.vb">
      <DependentUpon>rptGeneralDayBook.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmGeneralLedger.Designer.vb">
      <DependentUpon>frmGeneralLedger.vb</DependentUpon>
    </Compile>
    <Compile Include="frmGeneralLedger.vb" />
    <Compile Include="frmPurchaseDaybook.Designer.vb">
      <DependentUpon>frmPurchaseDaybook.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPurchaseDaybook.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSqlServerSetting.Designer.vb">
      <DependentUpon>frmSqlServerSetting.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSqlServerSetting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptGeneralLedger.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptGeneralLedger.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptPurchaseDayBook.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptPurchaseDayBook.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptSales2.vb">
      <DependentUpon>rptSales2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="rptPurchase.vb">
      <DependentUpon>rptPurchase.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="SIS_DBDataSet.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SIS_DBDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="SIS_DBDataSet.vb">
      <DependentUpon>SIS_DBDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="SIS_DBDataSet1.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SIS_DBDataSet1.xsd</DependentUpon>
    </Compile>
    <Compile Include="SIS_DBDataSet2.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SIS_DBDataSet2.xsd</DependentUpon>
    </Compile>
    <Compile Include="SIS_DBDataSet3.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SIS_DBDataSet3.xsd</DependentUpon>
    </Compile>
    <Compile Include="SubRPTexpenses.vb">
      <DependentUpon>SubRPTexpenses.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmOverallReport.Designer.vb">
      <DependentUpon>frmOverallReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmOverallReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SubRPTpurchases.vb">
      <DependentUpon>SubRPTpurchases.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="SubRPTservice.vb">
      <DependentUpon>SubRPTservice.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="SubRPTsales.vb">
      <DependentUpon>SubRPTsales.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmDebtorsReport.Designer.vb">
      <DependentUpon>frmDebtorsReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmDebtorsReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmProfitAndLossReport.Designer.vb">
      <DependentUpon>frmProfitAndLossReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmProfitAndLossReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPurchaseReport.Designer.vb">
      <DependentUpon>frmPurchaseReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPurchaseReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmStockInAndOutReport.Designer.vb">
      <DependentUpon>frmStockInAndOutReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmStockInAndOutReport.vb" />
    <Compile Include="frmVoucher.Designer.vb">
      <DependentUpon>frmVoucher.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVoucher.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVoucherRecord.Designer.vb">
      <DependentUpon>frmVoucherRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVoucherRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVoucherReport.Designer.vb">
      <DependentUpon>frmVoucherReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVoucherReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptCreditors.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptCreditors.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptDebtors.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptDebtors.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptExpenses.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptExpenses.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptOverall.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptOverall.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptQuotation1.vb">
      <DependentUpon>rptQuotation1.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmSMSSetting.Designer.vb">
      <DependentUpon>frmSMSSetting.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSMSSetting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptInvoice2.vb">
      <DependentUpon>rptInvoice2.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmServiceDoneReport.Designer.vb">
      <DependentUpon>frmServiceDoneReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmServiceDoneReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerRecord2.Designer.vb">
      <DependentUpon>frmCustomerRecord2.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerRecord2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmServiceBillingRecord.Designer.vb">
      <DependentUpon>frmServiceBillingRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmServiceBillingRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptInvoice1.vb">
      <DependentUpon>rptInvoice1.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmServicesRecord1.Designer.vb">
      <DependentUpon>frmServicesRecord1.vb</DependentUpon>
    </Compile>
    <Compile Include="frmServicesRecord1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmServiceBilling.Designer.vb">
      <DependentUpon>frmServiceBilling.vb</DependentUpon>
    </Compile>
    <Compile Include="frmServiceBilling.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmQuotationRecord1.Designer.vb">
      <DependentUpon>frmQuotationRecord1.vb</DependentUpon>
    </Compile>
    <Compile Include="frmQuotationRecord1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmQuotationRecord.Designer.vb">
      <DependentUpon>frmQuotationRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmQuotationRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSplash.Designer.vb">
      <DependentUpon>frmSplash.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSplash.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptQuotation.vb">
      <DependentUpon>rptQuotation.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="frmQuotation.Designer.vb">
      <DependentUpon>frmQuotation.vb</DependentUpon>
    </Compile>
    <Compile Include="frmQuotation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmServicesRecord.Designer.vb">
      <DependentUpon>frmServicesRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmServicesRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmServices.Designer.vb">
      <DependentUpon>frmServices.vb</DependentUpon>
    </Compile>
    <Compile Include="frmServices.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLogin.Designer.vb">
      <DependentUpon>frmLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="frmLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLogs.Designer.vb">
      <DependentUpon>frmLogs.vb</DependentUpon>
    </Compile>
    <Compile Include="frmLogs.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSupplierRecord.Designer.vb">
      <DependentUpon>frmSupplierRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSupplierRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerRecord1.Designer.vb">
      <DependentUpon>frmCustomerRecord1.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerRecord1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomerRecord.Designer.vb">
      <DependentUpon>frmCustomerRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomerRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomer.Designer.vb">
      <DependentUpon>frmCustomer.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCompany.Designer.vb">
      <DependentUpon>frmCompany.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCompany.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmContacts.Designer.vb">
      <DependentUpon>frmContacts.vb</DependentUpon>
    </Compile>
    <Compile Include="frmContacts.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="rptInvoice.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptInvoice.rpt</DependentUpon>
    </Compile>
    <Compile Include="frmSalesInvoiceRecord.Designer.vb">
      <DependentUpon>frmSalesInvoiceRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesInvoiceRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPOS.Designer.vb">
      <DependentUpon>frmPOS.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPOS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSalesReport.Designer.vb">
      <DependentUpon>frmSalesReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSalesReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmReport.Designer.vb">
      <DependentUpon>frmReport.vb</DependentUpon>
    </Compile>
    <Compile Include="frmReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmReportViewer.vb" />
    <Compile Include="frmReportViewer.Designer.vb">
      <DependentUpon>frmReportViewer.vb</DependentUpon>
    </Compile>
    <Compile Include="ReportManager.vb" />
    <Compile Include="frmStockIn.Designer.vb">
      <DependentUpon>frmStockIn.vb</DependentUpon>
    </Compile>
    <Compile Include="frmStockIn.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPurchaseRecord.Designer.vb">
      <DependentUpon>frmPurchaseRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPurchaseRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmProductRecord.Designer.vb">
      <DependentUpon>frmProductRecord.vb</DependentUpon>
    </Compile>
    <Compile Include="frmProductRecord.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPurchaseEntry.Designer.vb">
      <DependentUpon>frmPurchaseEntry.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPurchaseEntry.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmProduct.Designer.vb">
      <DependentUpon>frmProduct.vb</DependentUpon>
    </Compile>
    <Compile Include="frmProduct.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCamera.Designer.vb">
      <DependentUpon>frmCamera.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCamera.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Encryption.vb" />
    <Compile Include="frmAbout.Designer.vb">
      <DependentUpon>frmAbout.vb</DependentUpon>
    </Compile>
    <Compile Include="frmAbout.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmActivation.Designer.vb">
      <DependentUpon>frmActivation.vb</DependentUpon>
    </Compile>
    <Compile Include="frmActivation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSubCategory.Designer.vb">
      <DependentUpon>frmSubCategory.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSubCategory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangePassword.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangePassword.Designer.vb">
      <DependentUpon>frmChangePassword.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCategory.Designer.vb">
      <DependentUpon>frmCategory.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCategory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMainMenu.Designer.vb">
      <DependentUpon>frmMainMenu.vb</DependentUpon>
    </Compile>
    <Compile Include="frmMainMenu.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmRegistration.Designer.vb">
      <DependentUpon>frmRegistration.vb</DependentUpon>
    </Compile>
    <Compile Include="frmRegistration.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSupplier.Designer.vb">
      <DependentUpon>frmSupplier.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSupplier.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSystemInfo.Designer.vb">
      <DependentUpon>frmSystemInfo.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSystemInfo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ModCommonClasses.vb" />
    <Compile Include="ModCS.vb" />
    <Compile Include="ModFunc.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="rptSales.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSales.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptSales1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptSales1.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptService.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptService.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptServiceReceipt.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptServiceReceipt.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptStockIn.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStockIn.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptStockOut.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptStockOut.rpt</DependentUpon>
    </Compile>
    <Compile Include="rptVoucher.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptVoucher.rpt</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="frmSalesLocations.resx">
      <DependentUpon>frmSalesLocations.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmContacts_2.resx">
      <DependentUpon>frmContacts_2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerRecord6.resx">
      <DependentUpon>frmCustomerRecord6.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerRecord5.resx">
      <DependentUpon>frmCustomerRecord5.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerRecord4.resx">
      <DependentUpon>frmCustomerRecord4.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerRecord3.resx">
      <DependentUpon>frmCustomerRecord3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPaymentRecord_3.resx">
      <DependentUpon>frmPaymentRecord_3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPaymentRecord_2.resx">
      <DependentUpon>frmPaymentRecord_2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPayment_3.resx">
      <DependentUpon>frmPayment_3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPayment_2.resx">
      <DependentUpon>frmPayment_2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesmanRecord_3.resx">
      <DependentUpon>frmSalesmanRecord_3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesmanRecord_2.resx">
      <DependentUpon>frmSalesmanRecord_2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesReturnRecord.resx">
      <DependentUpon>frmSalesReturnRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesReturn.resx">
      <DependentUpon>frmSalesReturn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmBarcodeLabelPrinting.resx">
      <DependentUpon>frmBarcodeLabelPrinting.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomDialog14.resx">
      <DependentUpon>frmCustomDialog14.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomDialog15.resx">
      <DependentUpon>frmCustomDialog15.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomDialog5.resx">
      <DependentUpon>frmCustomDialog5.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmEmailSetting.resx">
      <DependentUpon>frmEmailSetting.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPurchaseReturn.resx">
      <DependentUpon>frmPurchaseReturn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPurchaseReturnRecord.resx">
      <DependentUpon>frmPurchaseReturnRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmRecoveryPassword.resx">
      <DependentUpon>frmRecoveryPassword.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptBarcodeLabelPrinting.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptBarcodeLabelPrinting.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCreditTermsStatements.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCreditTermsStatements.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCreditTermsStatementsByCustomer.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCreditTermsStatementsByCustomer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCreditTermsStatementsReport.resx">
      <DependentUpon>frmCreditTermsStatementsReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCreditTerms.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCreditTerms.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCreditTermsReport.resx">
      <DependentUpon>frmCreditTermsReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSendSMS.resx">
      <DependentUpon>frmSendSMS.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptInvoiceReturn.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptInvoiceReturn.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesmanLedger_2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesmanLedger_2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptServiceTaxReport.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptServiceTaxReport.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTaxReport.resx">
      <DependentUpon>frmTaxReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesmanCommissionReport.resx">
      <DependentUpon>frmSalesmanCommissionReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesmanLedger.resx">
      <DependentUpon>frmSalesmanLedger.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesmanRecord.resx">
      <DependentUpon>frmSalesmanRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesman.resx">
      <DependentUpon>frmSalesman.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSMS.resx">
      <DependentUpon>frmSMS.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCustomerLedger.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCustomerLedger.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerLedger.resx">
      <DependentUpon>frmCustomerLedger.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesmanCommission.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesmanCommission.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesmanLedger.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesmanLedger.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSalesTaxReport.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSalesTaxReport.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSupplierLedger.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSupplierLedger.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSupplierLedger.resx">
      <DependentUpon>frmSupplierLedger.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptTrialBalance.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptTrialBalance.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTrialBalance.resx">
      <DependentUpon>frmTrialBalance.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPaymentRecord.resx">
      <DependentUpon>frmPaymentRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPayment.resx">
      <DependentUpon>frmPayment.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmGeneralDayBook.resx">
      <DependentUpon>frmGeneralDayBook.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptGeneralDayBook.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptGeneralDayBook.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmGeneralLedger.resx">
      <DependentUpon>frmGeneralLedger.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPurchaseDaybook.resx">
      <DependentUpon>frmPurchaseDaybook.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSqlServerSetting.resx">
      <DependentUpon>frmSqlServerSetting.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptGeneralLedger.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptGeneralLedger.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchaseDayBook.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchaseDayBook.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSales2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSales2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptPurchase.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptPurchase.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="SubRPTexpenses.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>SubRPTexpenses.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmOverallReport.resx">
      <DependentUpon>frmOverallReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SubRPTpurchases.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>SubRPTpurchases.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="SubRPTservice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>SubRPTservice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="SubRPTsales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>SubRPTsales.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmDebtorsReport.resx">
      <DependentUpon>frmDebtorsReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmProfitAndLossReport.resx">
      <DependentUpon>frmProfitAndLossReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPurchaseReport.resx">
      <DependentUpon>frmPurchaseReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmStockInAndOutReport.resx">
      <DependentUpon>frmStockInAndOutReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVoucher.resx">
      <DependentUpon>frmVoucher.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVoucherRecord.resx">
      <DependentUpon>frmVoucherRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVoucherReport.resx">
      <DependentUpon>frmVoucherReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptCreditors.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptCreditors.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptDebtors.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptDebtors.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptExpenses.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptExpenses.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptOverall.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptOverall.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptQuotation1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptQuotation1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSMSSetting.resx">
      <DependentUpon>frmSMSSetting.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptInvoice2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptInvoice2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmServiceDoneReport.resx">
      <DependentUpon>frmServiceDoneReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerRecord2.resx">
      <DependentUpon>frmCustomerRecord2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmServiceBillingRecord.resx">
      <DependentUpon>frmServiceBillingRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptInvoice1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptInvoice1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmServicesRecord1.resx">
      <DependentUpon>frmServicesRecord1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmServiceBilling.resx">
      <DependentUpon>frmServiceBilling.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmQuotationRecord1.resx">
      <DependentUpon>frmQuotationRecord1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmQuotationRecord.resx">
      <DependentUpon>frmQuotationRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSplash.resx">
      <DependentUpon>frmSplash.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptQuotation.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptQuotation.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmQuotation.resx">
      <DependentUpon>frmQuotation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmServicesRecord.resx">
      <DependentUpon>frmServicesRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmServices.resx">
      <DependentUpon>frmServices.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLogin.resx">
      <DependentUpon>frmLogin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLogs.resx">
      <DependentUpon>frmLogs.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSupplierRecord.resx">
      <DependentUpon>frmSupplierRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerRecord1.resx">
      <DependentUpon>frmCustomerRecord1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomerRecord.resx">
      <DependentUpon>frmCustomerRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomer.resx">
      <DependentUpon>frmCustomer.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCompany.resx">
      <DependentUpon>frmCompany.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmContacts.resx">
      <DependentUpon>frmContacts.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rptInvoice.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptInvoice.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesInvoiceRecord.resx">
      <DependentUpon>frmSalesInvoiceRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPOS.resx">
      <DependentUpon>frmPOS.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSalesReport.resx">
      <DependentUpon>frmSalesReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmReport.resx">
      <DependentUpon>frmReport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmStockIn.resx">
      <DependentUpon>frmStockIn.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPurchaseRecord.resx">
      <DependentUpon>frmPurchaseRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmProductRecord.resx">
      <DependentUpon>frmProductRecord.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPurchaseEntry.resx">
      <DependentUpon>frmPurchaseEntry.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmProduct.resx">
      <DependentUpon>frmProduct.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCamera.resx">
      <DependentUpon>frmCamera.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmAbout.resx">
      <DependentUpon>frmAbout.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmActivation.resx">
      <DependentUpon>frmActivation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSubCategory.resx">
      <DependentUpon>frmSubCategory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmChangePassword.resx">
      <DependentUpon>frmChangePassword.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCategory.resx">
      <DependentUpon>frmCategory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmMainMenu.resx">
      <DependentUpon>frmMainMenu.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmRegistration.resx">
      <DependentUpon>frmRegistration.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSupplier.resx">
      <DependentUpon>frmSupplier.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSystemInfo.resx">
      <DependentUpon>frmSystemInfo.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSales.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptSales1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptSales1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptService.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptService.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptServiceReceipt.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptServiceReceipt.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStockIn.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStockIn.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptStockOut.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptStockOut.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="rptVoucher.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptVoucher.vb</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="bin\Debug\App.config" />
    <None Include="DataSet1.xsc">
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </None>
    <None Include="DataSet1.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSet1.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSet1.xss">
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </None>
    <None Include="My Project\app.manifest">
      <SubType>Designer</SubType>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="NewDataSet.xsc">
      <DependentUpon>NewDataSet.xsd</DependentUpon>
    </None>
    <None Include="NewDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>NewDataSet.Designer.vb</LastGenOutput>
    </None>
    <None Include="NewDataSet.xss">
      <DependentUpon>NewDataSet.xsd</DependentUpon>
    </None>
    <None Include="Sales and Inventory System_TemporaryKey.pfx" />
    <None Include="SIS_DBDataSet.xsc">
      <DependentUpon>SIS_DBDataSet.xsd</DependentUpon>
    </None>
    <None Include="SIS_DBDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SIS_DBDataSet.Designer.vb</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="SIS_DBDataSet.xss">
      <DependentUpon>SIS_DBDataSet.xsd</DependentUpon>
    </None>
    <None Include="SIS_DBDataSet1.xsc">
      <DependentUpon>SIS_DBDataSet1.xsd</DependentUpon>
    </None>
    <None Include="SIS_DBDataSet1.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SIS_DBDataSet1.Designer.vb</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="SIS_DBDataSet1.xss">
      <DependentUpon>SIS_DBDataSet1.xsd</DependentUpon>
    </None>
    <None Include="SIS_DBDataSet2.xsc">
      <DependentUpon>SIS_DBDataSet2.xsd</DependentUpon>
    </None>
    <None Include="SIS_DBDataSet2.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SIS_DBDataSet2.Designer.vb</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="SIS_DBDataSet2.xss">
      <DependentUpon>SIS_DBDataSet2.xsd</DependentUpon>
    </None>
    <None Include="SIS_DBDataSet3.xsc">
      <DependentUpon>SIS_DBDataSet3.xsd</DependentUpon>
    </None>
    <None Include="SIS_DBDataSet3.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SIS_DBDataSet3.Designer.vb</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="SIS_DBDataSet3.xss">
      <DependentUpon>SIS_DBDataSet3.xsd</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\splash.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\background screen.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\user regestration.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\logout.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\search invoice.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Billing.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\add stock.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new customers.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\find customer.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App.ico" />
    <Content Include="lOGO OF EPS.ico" />
    <Content Include="Picsart_23-03-19_11-27-15-052.ico" />
    <None Include="Resources\Picsart_23-03-19_11-27-15-0522.png" />
    <None Include="Resources\Picsart_23-03-19_11-27-15-0521.png" />
    <None Include="Resources\Picsart_23-03-19_11-27-15-052.png" />
    <None Include="Resources\peImage.EditValue3.png" />
    <None Include="Resources\peImage.EditValue2.png" />
    <None Include="Resources\panelControl1.ContentImage1.jpg" />
    <None Include="Resources\peImage.EditValue1.png" />
    <None Include="Resources\panelControl1.ContentImage.jpg" />
    <None Include="Resources\peImage.EditValue.png" />
    <None Include="Resources\Maximise-32X32.png" />
    <None Include="Resources\Button-Delete-icon11.png" />
    <None Include="Resources\User-Interface-Restore-Window-icon %281%29.png" />
    <None Include="Resources\Programming-Minimize-Window-icon.png" />
    <None Include="Resources\keyboard-icon %281%29.png" />
    <None Include="Resources\keyboard-icon.png" />
    <None Include="Resources\Button-Delete-icon1.png" />
    <None Include="Resources\ModernXP-09-Keyboard-icon %281%291.png" />
    <None Include="Resources\Database-Active-icon1.png" />
    <None Include="Resources\money-icon.png" />
    <None Include="Resources\basket-full-icon.png" />
    <None Include="Resources\Billing-icon.png" />
    <None Include="Resources\Utilities-icon.png" />
    <None Include="Resources\payment-icon.png" />
    <None Include="Resources\Users-icon.png" />
    <None Include="Resources\Admin-icon.png" />
    <None Include="Resources\Log-Out-icon.png" />
    <None Include="Resources\messages-icon.png" />
    <None Include="Resources\Inventory-icon.png" />
    <None Include="Resources\Stocks-icon.png" />
    <None Include="Resources\edit-file-icon.png" />
    <None Include="Resources\product-icon.png" />
    <None Include="Resources\product-sales-report-icon.png" />
    <None Include="Resources\report-icon.png" />
    <None Include="Resources\User-Group-icon.png" />
    <None Include="Resources\Database-Active-icon.png" />
    <None Include="Resources\log-icon.png" />
    <None Include="Resources\Actions-user-group-new-icon.png" />
    <None Include="Resources\warehouse_illu_01.png" />
    <None Include="Resources\packages %282%29.jpg" />
    <None Include="Resources\Entypo_d83d%280%29_512.png" />
    <None Include="Resources\1 %2816%29.png" />
    <None Include="Resources\Close_32x32.png" />
    <None Include="Resources\Activate.png" />
    <None Include="Resources\Action_Security_ChangePassword_32x32.png" />
    <None Include="Resources\Button-Close-icon %281%29.png" />
    <None Include="Resources\cancel-512.png" />
    <None Include="Resources\Apply_16x16.png" />
    <None Include="Resources\Excel-icon.png" />
    <None Include="Resources\Reset2_32x32.png" />
    <None Include="Resources\Voucher.png" />
    <None Include="Resources\Entypo_e731%280%29_512.png" />
    <None Include="Resources\record_512.png" />
    <None Include="Resources\Company1.png" />
    <None Include="Resources\quotation 256.png" />
    <None Include="Resources\service 256.png" />
    <None Include="Resources\login-512.png" />
    <None Include="Resources\stock in icon.png" />
    <None Include="Resources\Hotels_B-512.png" />
    <None Include="Resources\Summary.png" />
    <None Include="Resources\reports1.png" />
    <None Include="Resources\login_icon %281%29.gif" />
    <None Include="Resources\reports.png" />
    <None Include="Resources\12.jpg" />
    <None Include="Resources\photo.jpg" />
    <None Include="Resources\Product.png" />
    <None Include="Resources\supplier.png" />
    <None Include="Resources\Database.png" />
    <None Include="Resources\Logs.png" />
    <None Include="Resources\fevicon.jpg" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="CRYPTEXTLib">
      <Guid>{7444C709-39BF-11D1-8CD9-00C04FC29D45}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalPrintControlLib">
      <Guid>{62862E3F-F35B-455E-9459-49EAADE80A25}</Guid>
      <VersionMajor>13</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalReportsCommonObjectModelLib">
      <Guid>{FDB9B69E-BA79-4B13-9E96-8373AF162AEA}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalReportsDataDefModelLib">
      <Guid>{9B2F3E7B-8D45-47E8-95D3-3A9C1B22437F}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalReportsObjectFactoryLib">
      <Guid>{A25D82D0-1501-4A89-BA92-B7E5B002EC4F}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="Microsoft.Office.Core">
      <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>5</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="Microsoft.Office.Interop.Excel1">
      <Guid>{00020813-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>7</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="PAGEOBJECTMODELLib">
      <Guid>{237F4BEC-8AE5-41E1-AE84-B194E4670597}</Guid>
      <VersionMajor>13</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="SAXSERIALIZELib">
      <Guid>{6EF0D172-0FAB-4FC3-BDBB-C6C0D9D69A10}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="stdole">
      <Guid>{00020430-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="VBIDE">
      <Guid>{0002E157-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>5</VersionMajor>
      <VersionMinor>3</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="SAP.CrystalReports14.NET.2.0">
      <Visible>False</Visible>
      <ProductName>SAP Crystal Reports Runtime Engine for .NET Framework</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Reports\InvoiceReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\SalesReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\StockInReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\CustomerReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\SupplierReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\CustomerLedgerReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\SupplierLedgerReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\SalesTaxReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\README_ReportViewer_Guide.md">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>