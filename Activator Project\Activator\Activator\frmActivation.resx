﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAQEAAAAAAIAAoQgAAFgAAACgAAABAAAAAgAAAAAEAIAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAABgwAAC9fAAAsWU8AHz8AAA8fAAADBgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABMmAAU9cP8NRXj/CkV4/wI4a/8AMmXgACZMAAAZ
        MwAACRMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAfPwATToD/HE+C/wk8
        b/+Ossz/WJ/E/xtpmv8LSXv/ADNm/wAvX34AI0YAABMmAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAALFkAVo6y/zltnf8WSXz/GEl5/8rc6P9Krdb/N6PR/y+Yyf8ee63/EF2Q/wI5bP8AFixuAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAABgwAADJlh2O21f+81eX/Kl6R/xBDdv83YYv/xuLv/zum0v81otH/L6LT/ymj
        1v8RZJf/ACNG/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYsAApCdP9Lvd//k9br/5O20P8kV4r/DD9y/2SF
        p/+n1+v/NqPR/zKj0/8spNX/Gnyv/wAvX/8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAjRgA/cpn/wen0/0K5
        3P+65fL/Z5O3/x5RhP8IPG//mrDG/3vE4v80pdP/L6XV/ySXyf8AMWT/AAYMAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAJkwAO2+X//r9/v+Y2ez/Sbvd/87p8/9Fd6P/GEt+/w5Ac//B0d//VbXb/zGn1v8rp9j/Aztu/wAT
        Jl4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAkTAAAyZf94or//+/3+/3HL5f9dwuH/yd/r/y9jlf8SRXj/JFOB/8vi
        7f88rtn/LqnY/wtThv8AHDn+AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFiwAAjZp/7DN3v/t+Pz/VcDg/3zN
        5v+sxtr/JVmM/w1Ac/9KcZj/uuHw/y+r2f8VbqD/AClS/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAj
        Rh0RR3f/2+vz/9Pv9/9Eut3/pdzu/36jw/8gU4b/Cj1w/3yZtf+Q0+v/JZrK/wE3av8AFiwHAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAwYAAClS0zJmkf/1+/3/rOHw/0O43P/G6PP/VoOs/xpNgP8KPXD/rsHT/2PD
        5f8ag7T/ADJl/wAZMwAAEyYAAAwZAAAGDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJEwAAL1//Y5Kz//7+/v+C0ej/UL3e/87l
        7/85a5r/FEd6/xdIeP/I2+j/Qbni/xR3qP8HUIP/Az9y/wA0Z/8AMWTYAB8/JgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABMmAAA0
        Z/+cv9T/9vz9/2DF4v9nxuP/v9Tj/ylcj/8PQnX/M1+L/8Xl8v8stOD/I7Pj/x205v8XsOP/EJfK/wA2
        af8AEyYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAHDkACT9x/87i7P/h9Pn/Sb3e/4vV6v+Ytc7/IlWI/ws+cf9fgqX/puDy/ySz
        4v8ftOT/GrXn/xW26f8Lhrn/ADJl/wAJEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAApUpAkWYb/7vb6/7/o9P8+u97/s+Ty/2qS
        tv8cT4L/CTxv/5StxP920e3/Ibbl/xy25/8Wt+n/Erns/wZnmv8ALFn/AAMGAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgwAAC9f/0+B
        pv/8/v7/lNrt/0K+4f/L6fP/R3Wi/xZJfP8OQXP/vNDf/0zF6v8fuOf/Gbjp/xO57P8PuOv/AkyA/wAj
        Rs0AAAAAAAAAAAAAAAAAAAAAAAAAAAAGDAAAAwYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAMGQAAMmb/h6/I//v9/v9rzej/UsXl/8rf6/8vYpP/EEN2/yJSgf/J4+//L77p/xy5
        6f8Vuuz/ELvu/wus3/8AO27/ABw5GAAPHwAAGTMAACZMAAAvXwAAMWR3AClSxAADBgAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABkzAAQ6bP+91+X/7Pn8/07F5f9w0ev/r8ba/yNX
        iv8LPnH/RW+Y/7nn9v8eu+n/GLzs/xK87v8NvvH/B5bJ/wAyZv8AO27/AUd6/wtMf/9Upsf/ALPk/wBM
        f/8AI0a4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJkxGF09+/+Tx
        9//Q8Pj/O8Hk/5rf8v+Co8L/HlGE/wo9cP92l7X/jt71/xq97P8Uvu7/D7/x/wrA8/9Kyu//cKbI/x1Q
        g/8JPG//jajC/2rg/v8BtOb/ADlt/wAWLAsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAMGAAAsWfM8c5v/+f3+/6fk8/83wuX/vun1/1iCq/8YS37/Cj1x/6nA0/9c0fL/F7/u/xHA
        8f8MwfP/C8L1/7fr+v9Rf6n/Fkl8/w5BdP+5zt7/N9T9/wKYyv8AMmX/AAkTAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACRMAADJl/3Cgvv/+/v7/edfu/0HH6f/M5vH/OmqZ/xJF
        eP8WSHr/xd3p/zXI8f8UwfH/DsLz/wnC9f8byfj/x+Xx/zVnl/8QQ3b/I1OC/8Xk8f8Wzfz/AXWn/wAv
        X/8AAwYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAATJgABNmn/qcvd//X8
        /v9Uzuz/V8/t/8HV5P8oW43/DUBz/zFgjf/E6vb/HcTx/xDD8/8KxPb/BcX4/z7U+/+30OH/JViM/ws+
        cf9HcZn/suv7/wbG+P8BVIf/ACZM6wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAB8/Eg1Gd//W6vP/3vb7/zzI6/982/L/nbbO/yBThv8KPXD/WoOo/6Xo+v8SxPP/DcX2/wfG
        +f8Dx/r/cuH+/4ytyf8fUoX/CTxv/3mZt/+A4v3/Bbjr/wA+cf8AHDk6AAAAAAAAAAAAAwYAAAwZAAAJ
        EwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADBgAAKVLEK2aR//P7/f+67Pj/MMfr/6jn9/9ukrb/Gk2A/wk8
        b/+Nrsf/ct34/w/G9f8Jx/n/BMj7/wHK/f+o7P7/Xomw/xhLfv8LPnH/rMLV/0nV+v8FoNP/ADRn/wAs
        WQAAMmU6ADBjpAAxZM0AMGPPADFksgAyZVMAJkwAABw5AAAPHwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAkTAAAvX/9akbP//v///4zg
        9P8zyu3/xuv2/0l1of8UR3r/DkJ1/7fS4f9C0/j/DMj4/wbJ+/8Byv7/Ds3//8Tq9v89bp3/EkV4/xpL
        fP+74O7/DMb4/waazf8GjL//B6HV/wqw4/8Mtej/DLPl/wqo2/8Jlcj/B3yv/wVajf8BN2r/AC9fqwAf
        PwAABgwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAADx8AADRn/5O+1P/7/v7/YNby/0PQ8P/K4Oz/MGGS/w9Cdf8hVYT/xufz/yHO+f8Iyvv/Asv+/wDL
        //8t0/7/wdno/ylcj/8NQXT/CbLl/xXH+P8oy/j/NM74/znM9v82x/L/MMTw/yrD8f8lxPP/Hsb1/xbF
        9f8Pw/b/DLzv/wmOwf8EUoX/ADJl/wAfPwAAAwYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcOQAGP3H/xuLt/+r5/f9A0PD/YNj0/7TI2v8iVYj/Cz5x/0Fz
        nf+47vz/Dcz7/wXN/v8BzP//Acr+/1vc/f9Fwur/Mr7p/03T+f9Iw+z/U8Xq/27N6v+F2O7/l+Pz/6Ts
        +P+n7vn/nej2/4zg8/9s1/L/Psnw/xW+8P8LwvX/CsL1/wmd0P8ETYD/AC9f/QAPHwAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAClSfB1Zh//p9/v/y/L7/y3N
        8f+M5Pj/iKXD/xxPgv8KPG//bpy7/43n/f8Hzv7/A87//wPL/v8v0/3/Xdb3/1DA5/9rxuX/luPz/6Ps
        9/+g6/f/n+n1/5/m8v+f4+7/n+Hs/6Lg6/+o4uz/r+fv/7zw9f+z8/r/ctnz/x7A8P8JwvX/CsH0/wh+
        sf8ANGf/ABw5AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD
        BgAAL1//RoKo//v+//+f6fn/KM7z/7Xs+f9chKv/Fkl8/ws/cv+gxNn/V97+/xTS//9g3v7/WMLo/2TB
        4v+O4fP/j+r7/4/r+v+R6Pj/lOj3/5np9/+e6/f/ou34/6bt9/+o7Pb/qOnz/6Tj7f+e2eX/ndbi/6zi
        6v+68vf/btjz/xG/8f8Jw/b/CaDU/wI9cP8AHz9RAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAwZAAAyZf98sMv//v7//2/f9/8x0vX/yejz/ztqmf8QQ3b/FVCB/1zg
        /v943/n/Ubbe/3bQ6/9+5fr/f+n9/4Tq/f+J6vz/jev8/5Lt/P+W7vz/m+/8/5/w/P+k8v3/qPP9/6z0
        /f+x9v3/tff9/6/u9v+j3uj/mdLf/6vg6f+n7Pf/K8Pv/wnC9f8JreD/AkBz/wAfP2kAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFiwAAjpt/7TY5//0/P7/SNj2/0fY
        +P/D1+X/J1qN/zN1n/+F3/f/T7Ha/3LW8P9u5v7/ceb9/3bm/P975/z/gOj8/4Xp/P+J6vv/juv7/5Ps
        +/+X7fz/m+/8/5/w/P+k8fz/qPP8/6z0/f+x9v3/tff+/7Dv9v+b1+P/mtLg/7Lt9P89x+//CcL1/wms
        3/8BOm3/ABkzHwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAj
        RjcST3//3vL4/9v3/f8u1Pf/S9v5/zvV9/+T4/f/TK7Y/2fW8f9j5f7/Z+T9/2nk/f9t5Pz/cuT7/3fl
        +/985vv/gOb7/4Xo+/+K6fv/jur7/5Ps+/+X7fv/m+77/5/v/P+j8fz/qPP8/6z0/f+w9v3/tPb9/6Df
        6v+Tztz/runx/zjF7v8JwvX/CJnM/wAzZv8ADBkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAwYAACxZ6TNznP/3/f7/s/D8/y3W+f+f6/v/Tq7Y/13P7v9Y5f//XOT+/1/j
        /f9i4vz/ZeH8/2nh+/9u4vr/c+L6/3jj+v985Pr/geb6/4bn+v+K6Pr/jun6/5Pr+v+X7Pv/m+77/5/v
        +/+j8fz/qPL8/6z0/f+w9v3/oeLt/4/L2/+n5vH/IsDu/wnC9v8GcKP/ACxZ/wADBgAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJEwAAL1//ZaHA/9T3/f+a7Pz/ar7h/1G+
        4/9P4/7/UeP//1Ti/v9X4f3/W+D8/17f+/9h3/v/Zd/6/2rf+v9v4Pn/dOH5/3jj+f995Pn/geX5/4bm
        +f+K5/n/jun6/5Lr+v+X7Pr/m+37/5/v+/+j8fz/p/L8/6z0/f+Z3er/jszc/43d8P8OvvH/Crzv/wJA
        dP8AGTNWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB8/AAVU
        hf9b4fr/p+Hz/0Cn1P9K3fr/SOL//0ri//9N4f7/UOD9/1Pf/P9W3vv/Wt36/13d+v9h3Pn/Zt34/2ve
        +P9w3/j/dOD4/3ji+P994/j/geT4/4bm+f+K5/n/jun5/5Pq+v+W7Pr/mu37/5/v+/+j8Pz/pvH8/43S
        4/+W0+L/Ucrt/wrB9P8Iibz/AC9f/wADBgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAvXwAYk73/svH8/1uy2f9Fxun/QuD9/0Lh//9D4f//RuD+/0ne/f9M3fz/T9z7/1Lb
        +v9V2vn/Wdr4/17a+P9i2/f/Z9z3/2zd9/9w3vf/dd/3/3nh9/994vj/geT4/4bl+P+K5/n/juj5/5Lq
        +v+W7Pr/mu37/57v+/+c6vb/gsjc/5Pa6/8Tuu3/C77x/wI/cv8AEyZUAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPHwAAN2voWNjy/7nn9P84odH/PN37/zzg/f874f//PeD//0Df
        /v9C3fz/Rdz7/0jb+v9L2fn/T9j4/1HX+P9V1/f/Wtf2/1/Y9v9j2fb/aNv2/2zc9v9x3fb/dd/2/3ng
        9/9+4ff/guP4/4bl+P+K5vj/juj5/5Lq+v+W6/r/mu37/4nX6P+LzuD/SMTq/wy/8v8Hdaj/AClS/wAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHz8ADGWU/6Tw/P98w+H/N7nh/zfe
        /P823/3/NeD//zff//853v7/PNz8/z/b/P9B2fv/RNj5/0fX+P9K1ff/TtX3/1HU9v9W1fX/W9b1/2DX
        9f9k2PX/adn1/23b9f9x3fb/dd72/3nf9v9+4ff/geP3/4bl+P+K5vj/jej5/5Lq+v+R5vX/esbb/3jN
        5/8Pu+//C6XY/wAxZP8ABgwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACxZACCW
        vf/c+v7/SaTS/zPO8P8x3vz/Md/+/zDg//8x3///M93+/zXb/f842vv/O9j6/z7X+f9B1fj/RNT2/0fT
        9f9J0vX/TtL0/1PS9P9X1PT/XNX0/2HW9P9l1/T/adn0/23a9P9y3PX/dt71/3nf9v9+4ff/guP3/4Xk
        +P+K5vj/juj5/3rN4/+J0OT/G7Xn/w2+8f8CPXD/ABMmXwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAMGAAAxZFdTw9//1/H5/zGazf8y3fr/Lt78/yvf/v8r3///K97//y7d/v8w2/3/Mtn7/zTX
        +v831vn/OtT3/z3S9v9A0fX/Q8/0/0bP9P9Lz/L/T9Dy/1TR8v9Y0vL/XdPz/2HV8/9m1/P/atjz/23Z
        9P9y2/X/dt31/3rf9v994Pb/geL3/4Xk+P981ev/f8ng/ze75v8PvfD/BVuO/wAcOf4AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGQAANGfqhOP1/7jg8P8zp9X/NOD7/zDf/P8r3/3/J9/+/ybe
        //8o3P7/Kdr9/yzY+/8u1vr/MdT4/zTS9/820fb/Oc/1/z3O8/8/zPL/Q8zx/0fM8f9MzfH/UM7x/1TP
        8f9Z0fH/XtPy/2LU8v9m1vL/atfz/27Z9P9y2/T/dd31/3ne9v994Pb/etnv/3HE3f9PvuT/Erzv/whx
        pf8AJkz/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADBkAAT1w/6by/f+j1On/NbHb/zbh
        +/8y4fz/LeD9/yng/v8m3///JNz9/yTZ/f8m1/v/KNX6/yrT+P8t0ff/MM/1/zPO9P81zPP/Ocry/zvJ
        8f8/yfD/RMnv/0jK7/9My/D/Uc3w/1bP8P9Z0PD/XtLx/2LU8v9m1fL/atfz/23Z8/9y2/T/dd31/3Xa
        8v9pwt3/Wbzg/xm97v8Lf7L/ACZM/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwZAANF
        d/+79v7/ms7n/za33/844vv/NOL8/y/i/f8r4v//KeD+/ybd/v8k2vz/I9f7/yPU+v8l0/n/J9D3/yrO
        9f8szPT/L8ry/zPI8f81x/D/OMbv/zzG7v9Bxu7/Rcju/0rJ7v9Oy+7/Uszv/1bN7/9az/D/XtHw/2LT
        8f9m1fL/atfy/27Z8/9w2PL/ZMLe/1q53f8eve3/DYW4/wAmTP8AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAMGQADRXf/xPj+/5vO5v83uN//O+T7/zbj/P8y4/3/LeP//yvh/v8p3v7/J9v8/yXY
        +/8j1fr/IdL4/yHP9/8jzfX/Jsv0/ynJ8v8sx/D/L8Xv/zHD7v80wu3/OcLs/z3E7P9Cxez/Rsbt/0rI
        7f9Oye3/Usvu/1bN7/9az+//XtHw/2LT8f9m1fL/aNTx/17B3/9Wtdv/Jb3s/w+Et/8AJkz/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADBkAAT1w/7/2/v+m0+n/N7Tc/z3m+/855fz/NOT+/zDk
        //8t4v7/K9/+/ync/P8n2fv/Jdb6/yTU+P8i0Pb/IM31/yDK8/8jyPL/Jcbw/yjD7v8rwe3/LsDs/y+4
        5P8iibb/HnWi/yF1ov8ribX/Qrjf/0vH7P9Pye3/U8vt/1bM7v9azu//XtDw/1/P7v9bv9//TbLa/yy+
        7P8PfbD/ACZM/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwZAAA0Z/+t6/b/vt7u/zWq
        1v8/6Pz/O+b9/zbm/v8y5f//MOP+/y7h/f8r3vz/Kdv7/yjY+f8m1fj/JNL3/yLO9f8hy/P/IMfx/yDE
        7/8iwu7/JcDs/xqMu/8FPW//JUtw/xgxSf8eNk3/J0ts/ww/cP8ngKz/R8Xr/0vG6/9PyOz/U8rt/1bM
        7v9Xyer/Wb3e/0Sv2v8xvev/C22g/wAmTP8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD
        BgAAMWTXis3h/+Px+P8xnM7/Qej8/z3n/f855/7/Neb//zLk/v8w4v3/Lt/9/yvc+/8q2fr/KNb4/ybT
        9/8k0PX/I8zz/yLJ8v8hxfD/H8Ht/xWNvP8RQXH/LDpI/wAAAAAAAAAAAAAAAAAAAAAZIikAF0Rv/yJ4
        pf9Ewun/SMPq/0zG6/9PyOz/T8Pn/1i63v86rdv/NL3q/wdXiv8AGTP/AAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAACxZL1igvv/+/v7/RaLQ/0Da8/8/6f3/O+j+/zfo//815v7/MuP9/zDg
        /f8u3fv/LNr6/yrY+f8o1ff/J9H1/yXO9P8kyvL/I8fw/yC85/8CPG//KjdF/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAABAXHQAOP3D/MqLO/0C/6P9Ewen/SMPq/0e+5P9Ps9r/PLDe/zK66P8CO27/AA8f6gAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAfPwAaaZT/+////4DA3/87wOP/Qur9/z7q
        /v866f//N+f//zXk/f8y4v3/MN/7/y7c+v8s2fn/Ktb3/ynT9v8nz/T/Jszz/yXI8P8Wj7z/HkNq/wAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHjxZABtxof85uuX/Pbzm/0C+5/9CueH/Q6bT/0i8
        5v8mnMz/ADJl/wADBh0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADx8AADdq/8Xs
        9f/L5vL/M6DQ/0Tq/P9A6/7/POr//zro//835v7/NeP8/zPg/P8x3fr/L9r5/y3X+P8r1Pb/KtH0/yjO
        8v8nyvH/EXak/xw2UP8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACc6SwANVYb/Mrbj/za3
        5P85uOP/QrHc/z+n1v9OwOj/EWye/wApUv8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAvX5dkoL3//v7+/1es1v8+zOr/Quz+/z7r//886f//Oef+/zfk/P814fz/M976/zHc
        +f8v2fj/LdX2/yvS9f8qz/P/KMzx/xF1ov8RKkP/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAApOksAClGE/yux4f8ws+H/M7Ti/zyj0v9Qt+D/QLbi/wM8b/8AEyb/AAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGTMACUh5/+X3+/++4vH/M5/P/0Pp+/9B7P//Pur//zzo
        /v855f3/N+P8/zXg+/8z3fn/Mdr4/y/X9/8t1PX/LNHz/yrN8v8Zjrn/GT1h/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAFTBMAA9mmf8lrN7/Ka7f/zOo2P9ApdP/YMPo/xx9rv8AL1//AAMGBAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGAAAvX+honrv//v///2q5
        3P85uN7/Q+7//0Hs/v8+6f7/POf9/znk/P834fv/Nd/6/zPc+P8x2ff/L9X1/y7S9P8tz/L/KMHm/wQ6
        bP8PGCIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGB0hABFAcBgbjsH/I6jc/yam2f80m83/Yr7j/0iz
        3f8DPXD/ABkz/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAFiwAAztt/8nj7f/p9vv/QaPR/z3M6f9D7f7/Qev+/z7o/f885fz/OuP7/zfg+v813fj/NNr3/zLX
        9v8w1PT/L9Dy/y3N8f8ZibT/Djpn/xUdJgAAAAAAAAAAAAAAAAAAAAAAFxsgAAkwWAANXpH/JKjb/yWm
        2f8wms3/WbTc/2jE5/8TZJf/ACxZ/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAmTEYmXon/8Pn8/8rq9f83ns7/PtHt/0Ps/v9A6f3/Puf8/zzk
        +/864fr/ON/5/zbc9/802fb/MtX0/zHS8/8vz/H/Lszv/xiCrv8HOmz/ID9fCB4xRAAjNEcAHztYAA8/
        bygPYZP/JKXY/yam2f8wm87/Ua3X/3rJ6P8si7r/ADJl/wAMGfQAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwYAAC9f/0t+ov/6/v7/v+by/zee
        zv88yOf/Quv9/0Do/P8+5vv/POP6/zng+f843fj/Ntr2/zTX9f8y1PP/MdDy/y/N8P8uyu7/J7Pb/xZ6
        p/8NXIz/C1eI/xNunv8hmcj/KK7d/ymn2f8wm87/U63X/4XN6f8/nsn/Ajhr/wAWLP8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJ
        EwAAL17/WYmq//r9/v/M7Pb/Q6XS/ziy2/9B4vj/QOf8/z7k+/884vn/Ot/4/zjc9/822fX/NdX0/zPS
        8v8yz/D/MMvu/y/H7P8uxOr/LcDo/yy85v8ruOP/K7Tg/y2n1/8yms3/ZLbc/5DQ6/9Focv/Azxv/wAf
        P/8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAkTAAAvX/9JeZ3/7/j6/+r4/P9wv+D/M53O/zrB5P8/4fj/PuP6/zzg
        +P863ff/ONr2/zfX9P811PL/NNDx/zLN7/8xye3/MMbq/y/C6P8uvub/LrHd/zGf0P9CotH/hsrn/5XU
        7P87k7//Ajls/wAfP/8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACRMAACxZ/yJWgv/B2eX//////8fp
        9P9ittv/MpvN/zey2/85yOn/O9fz/zrc9v852fX/N9bz/zXS8f80z/D/M8ns/zK95P8xrtr/MZ7Q/0Gh
        0P98xOP/q+Dy/4PO6f8hcaH/ADNm/wAZM/8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD
        BgAAI0bJAjVo/16Kqv/d7fT//////9rx+P+Tzeb/W7DX/zebzf8xnM3/M6LS/zOl0/8zo9P/MZ7P/zKZ
        zP9Go9H/bLrd/5nV6/+86vf/n9/y/0icwv8GRnj/AC9f/wAPH/8AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGQAALFn/BDls/1ODpf+z0eD/9fz+//3////y/P7/1+/4/7/k
        8v+03/D/s+Dw/73m9P/N8Pn/1PX7/8Pv+v+M1Ov/QZK4/whLff8AMmX/ABw5/wADBiYAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAATJhEAJkz/ADFk/wtG
        d/9BeJ7/cKC8/5G80/+g0OL/otbn/5jR5f+Dwdn/Y6fF/zaBqv8LVYb/ADVo/wAvX/8AHDn/AAYMXAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAGDAAAEyaAACNG/wApUv8AMmX/ADFk/wAxZP8AMWT/ADFk/wAvX/8AJkz/ABw5/wAM
        GcIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA+//////////wf/////////AH////////8AD////////gAP///////+AA////////4AD/////
        ///gAH///////+AAf///////8AB////////wAD////////gAP////////AAB///////+AAH///////8A
        AP///////wAAf///////gAA////////AAB5//////+AAAD//////4AAAH//////wAAAf//////gAAA//
        /////AAAB//////8AAAD//////4AAAIH/////wAAAAB/////gAAAAB/////AAAAAB////8AAAAAD////
        4AAAAAD////wAAAAAH////gAAAAAP///+AAAAAA////8AAAAAB////4AAAAAD////wAAAAAP////AAAA
        AAf///4AAAAAB////gAAAAAH///+AAAAAAP///wAAAAAA////AAAAAAD///8AAAAAAP///wAAAAAA///
        /AAAAAAD///8AAAAAAP///wAAAAAA////AAAB8AD///8AAAP4AP///4AAB/wA////gAAH/AH///+AAAf
        8Af///8AAB/wB////wAAH+AP////gAAP4B////+AAAOAH////8AAAAA/////4AAAAH/////wAAAA////
        //gAAAH//////AAAA///////AAAH//////+AAB////////AA//8=
</value>
  </data>
</root>