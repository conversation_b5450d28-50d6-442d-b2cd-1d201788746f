The software comprising backport-util-concurrent is based in large
part on the code from JSR166, and the package dl.util.concurrent.
The software has been released to the public domain, as explained at:
http://creativecommons.org/licenses/publicdomain, excepting portions
of the class
edu.emory.mathcs.backport.java.util.concurrent.CopyOnWriteArrayList,
which were adapted from class java.util.ArrayList, written by Sun
Microsystems, Inc, which are used with kind permission, and subject
to the following:

Copyright 2002-2004 Sun Microsystems, Inc. All rights reserved. Use is
subject to the following license terms.

  "Sun hereby grants you a non-exclusive, worldwide, non-transferrable
  license to use and distribute the Java Software technologies as part
  of a larger work in source and binary forms, with or without
  modification, provided that the following conditions are met:

   -Neither the name of or trademarks of <PERSON> may be used to endorse or
    promote products derived from the Java Software technology without
    specific prior written permission.

   -Redistributions of source or binary code must be accompanied by the
    following notice and disclaimers:

    Portions copyright Sun Microsystems, Inc. Used with kind permission.

    This software is provided AS IS, without a warranty of any kind.  ALL
    EXPRESS OR IMPLIED CONDITIONS, REPRESENTATIONS AND
    WARRANTIES, INCLUDING ANY IMPLIED WARRANTY OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PUPOSE OR
    NON-INFRINGEMENT, ARE HEREBY EXCLUDED. SUN
    MICROSYSTEMS, INC. AND ITS LICENSORS SHALL NOT BE LIABLE
    FOR ANY DAMAGES SUFFERED BY LICENSEE AS A RESULT OF
    USING, MODIFYING OR DISTRIBUTING THE SOFTWARE OR ITS
    DERIVATIVES. IN NO EVENT WILL SUN MICROSYSTEMS, INC. OR
    ITS LICENSORS BE LIABLE FOR ANY LOST REVENUE, PROFIT OR
    DATA, OR FOR DIRECT, INDIRECT,CONSQUENTIAL, INCIDENTAL
    OR PUNITIVE DAMAGES, HOWEVER CAUSED AND REGARDLESS OF
    THE THEORY OR LIABILITY, ARISING OUT OF THE USE OF OR
    INABILITY TO USE SOFTWARE, EVEN IF SUN MICROSYSTEMS, INC.
    HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

    You acknowledge that Software is not designed, licensed or intended for
    use in the design, construction, operation or maintenance of any nuclear
    facility."
