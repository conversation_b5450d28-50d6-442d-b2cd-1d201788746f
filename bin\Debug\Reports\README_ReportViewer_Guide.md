# دليل تحويل التقارير من Crystal Reports إلى ReportViewer

## نظرة عامة
تم تحويل نظام التقارير من Crystal Reports إلى Microsoft ReportViewer لتوفير المزايا التالية:
- إمكانية تعديل التقارير بسهولة من قبل المستخدم
- عدم الحاجة لتثبيت Crystal Reports Runtime
- أداء أفضل وحجم ملفات أصغر
- دعم أفضل للغة العربية

## الملفات الجديدة المضافة

### 1. frmReportViewer.vb
فورم عرض التقارير الجديد الذي يحتوي على:
- ReportViewer Control
- أزرار التصدير (PDF, Excel)
- أزرار الطباعة والإغلاق
- دوال عرض التقارير المختلفة

### 2. ReportManager.vb
كلاس إدارة التقارير الذي يحتوي على:
- دوال عرض جميع أنواع التقارير
- دوال تحميل البيانات من قاعدة البيانات
- دوال مساعدة للتقارير المخصصة

### 3. مجلد Reports
يحتوي على ملفات التقارير بصيغة RDLC:
- InvoiceReport.rdlc - تقرير الفواتير
- SalesReport.rdlc - تقرير المبيعات
- PurchaseReport.rdlc - تقرير المشتريات
- StockReport.rdlc - تقرير المخزون
- CustomerReport.rdlc - تقرير العملاء
- SupplierReport.rdlc - تقرير الموردين
- ProfitLossReport.rdlc - تقرير الأرباح والخسائر

## كيفية إنشاء تقرير جديد

### الخطوة 1: إنشاء ملف RDLC
1. افتح Visual Studio
2. أضف عنصر جديد من نوع "Report"
3. اختر "Report Wizard" لإنشاء التقرير بسهولة
4. حدد مصادر البيانات والحقول المطلوبة
5. احفظ الملف في مجلد Reports

### الخطوة 2: إضافة دالة تحميل البيانات
أضف دالة في ReportManager.vb لتحميل البيانات:

```vb
Private Shared Sub LoadYourReportData(parameters..., ds As DataSet)
    Try
        con = New SqlConnection(cs)
        con.Open()
        
        Dim cmd As New SqlCommand("YOUR_SQL_QUERY", con)
        ' أضف المعاملات إذا لزم الأمر
        cmd.Parameters.AddWithValue("@param1", value1)
        
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable("YourDataTable")
        adp.Fill(dt)
        ds.Tables.Add(dt)
        
        LoadCompanyDataStatic(ds)
        con.Close()
    Catch ex As Exception
        If con.State = ConnectionState.Open Then con.Close()
        Throw ex
    End Try
End Sub
```

### الخطوة 3: إضافة دالة عرض التقرير
أضف دالة عامة في ReportManager.vb:

```vb
Public Shared Sub ShowYourReport(parameters...)
    Try
        Dim ds As New DataSet()
        LoadYourReportData(parameters..., ds)
        
        Dim reportParameters As New List(Of ReportParameter)()
        reportParameters.Add(New ReportParameter("ParamName", "ParamValue"))
        reportParameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
        
        Dim frmReport As New frmReportViewer()
        frmReport.ShowCustomReport(Application.StartupPath & "\Reports\YourReport.rdlc", ds, reportParameters)
        frmReport.ShowDialog()
        
    Catch ex As Exception
        MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Try
End Sub
```

### الخطوة 4: استدعاء التقرير
في الفورم المطلوب، استدعي التقرير:

```vb
Private Sub btnShowReport_Click(sender As Object, e As EventArgs) Handles btnShowReport.Click
    ReportManager.ShowYourReport(parameter1, parameter2)
End Sub
```

## التقارير المحولة

### تم تحويل التقارير التالية:
- ✅ تقرير الفواتير (frmPOS)
- ✅ تقرير المبيعات (frmSalesReport)
- ✅ تقرير المدينين (frmDebtorsReport)
- ⏳ تقرير المشتريات
- ⏳ تقرير المخزون
- ⏳ تقرير العملاء
- ⏳ تقرير الموردين
- ⏳ تقرير الأرباح والخسائر

### التقارير المتبقية للتحويل:
- تقرير الدائنين
- تقرير دفتر الأستاذ العام
- تقرير دفتر اليومية
- تقرير الضرائب
- تقرير عمولات المندوبين
- تقرير دفتر أستاذ العميل
- تقرير دفتر أستاذ المورد

## نصائح مهمة

### 1. تصميم التقارير
- استخدم خطوط عربية مناسبة (Arial, Tahoma)
- اضبط اتجاه النص من اليمين إلى اليسار
- استخدم ألوان مناسبة للطباعة

### 2. الأداء
- تجنب تحميل بيانات كثيرة في تقرير واحد
- استخدم المعاملات لتصفية البيانات
- أغلق الاتصال بقاعدة البيانات دائماً

### 3. معالجة الأخطاء
- استخدم Try-Catch في جميع دوال التقارير
- اعرض رسائل خطأ واضحة للمستخدم
- سجل الأخطاء في ملف Log إذا لزم الأمر

## المتطلبات التقنية

### NuGet Packages المطلوبة:
```xml
<package id="Microsoft.ReportingServices.ReportViewerControl.Winforms" version="150.1484.0" targetFramework="net40" />
```

### References المطلوبة:
- Microsoft.ReportViewer.Common
- Microsoft.ReportViewer.DataVisualization
- Microsoft.ReportViewer.ProcessingObjectModel
- Microsoft.ReportViewer.WinForms

## استكمال التحويل

لاستكمال تحويل باقي التقارير:
1. حدد التقرير المراد تحويله
2. انشئ ملف RDLC جديد
3. أضف دالة تحميل البيانات
4. أضف دالة عرض التقرير
5. حديث الفورم المستدعي للتقرير
6. اختبر التقرير والتأكد من صحة البيانات

## الدعم والمساعدة

في حالة وجود مشاكل أو استفسارات:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات الأمثلة الموجودة
3. تأكد من صحة SQL Queries
4. تحقق من أسماء الحقول في DataSet و RDLC
