﻿Imports System.Data.SqlClient
Public Class frmLogin
    Dim frm As New frmMainMenu
    Declare Function Wow64DisableWow64FsRedirection Lib "kernel32" (ByRef oldvalue As Long) As Boolean
    Declare Function Wow64EnableWow64FsRedirection Lib "kernel32" (ByRef oldvalue As Long) As Boolean

    Private Sub OK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OK.Click
        If Len(Trim(UserID.Text)) = 0 Then
            MessageBox.Show("من فضلك ادخل اسم المستخدم", "", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            UserID.Focus()
            Exit Sub
        End If
        If Len(Trim(Password.Text)) = 0 Then
            MessageBox.Show("ادخل الرقم السري", "", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Password.Focus()
            Exit Sub
        End If
        Try
            con = New SqlConnection(cs)
            con.Open()
            cmd = con.CreateCommand()
            cmd.CommandText = "SELECT UserID, Password, Active, ExpiryDate FROM Registration WHERE UserID = @d1 AND Password = @d2"
            cmd.Parameters.AddWithValue("@d1", UserID.Text)
            cmd.Parameters.AddWithValue("@d2", Encrypt(Password.Text))
            rdr = cmd.ExecuteReader()

            If rdr.Read() Then
                Dim expiryDate As DateTime = Convert.ToDateTime(rdr("ExpiryDate"))
                If expiryDate < DateTime.Now Then
                    MessageBox.Show("انتهت الفترة التجريبية. يرجى تجديد الاشتراك.", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Exit Sub
                End If
                UserType.Text = rdr("UserType").ToString.Trim
                frm.lblUser.Text = UserID.Text
                frm.lblUserType.Text = UserType.Text

                Dim st As String = "Successfully logged in"
                LogFunc(UserID.Text, st)
                Me.Hide()
                frm.Show()
            Else
                MsgBox("تحقق من اسم المستخدم وكلمة السر", MsgBoxStyle.Critical, "خطأ")
                UserID.Text = ""
                Password.Text = ""
                UserID.Focus()
            End If
            rdr.Close()
            con.Close()
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub Cancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancel.Click
        End
    End Sub

    Private Sub LoginForm1_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Panel1.Location = New Point(Me.ClientSize.Width / 2 - Panel1.Size.Width / 2, Me.ClientSize.Height / 2 - Panel1.Size.Height / 2)
        Panel1.Anchor = AnchorStyles.None
    End Sub

    Private Sub frmLogin_FormClosing(sender As System.Object, e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        End
    End Sub

    Private Sub btnChangePassword_Click(sender As System.Object, e As System.EventArgs) Handles btnChangePassword.Click
        Me.Hide()
        frmChangePassword.Show()
        frmChangePassword.UserID.Text = ""
        frmChangePassword.OldPassword.Text = ""
        frmChangePassword.NewPassword.Text = ""
        frmChangePassword.ConfirmPassword.Text = ""
        frmChangePassword.UserID.Focus()
    End Sub

    Private Sub btnRecoveryPassword_Click(sender As System.Object, e As System.EventArgs) Handles btnRecoveryPassword.Click
        Me.Hide()
        frmRecoveryPassword.Show()
        frmRecoveryPassword.txtEmailID.Text = ""
        frmRecoveryPassword.txtEmailID.Focus()
    End Sub
End Class
