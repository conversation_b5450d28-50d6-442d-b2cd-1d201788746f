<?xml version="1.0" standalone="yes"?>
<xs:schema id="NewDataSet" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="NewDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Table1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ST_ID" type="xs:int" minOccurs="0" />
              <xs:element name="Date" type="xs:dateTime" minOccurs="0" />
              <xs:element name="InvoiceNo" type="xs:string" minOccurs="0" />
              <xs:element name="Name" type="xs:string" minOccurs="0" />
              <xs:element name="SubTotal" type="xs:double" minOccurs="0" />
              <xs:element name="Discount" type="xs:double" minOccurs="0" />
              <xs:element name="FreightCharges" type="xs:double" minOccurs="0" />
              <xs:element name="OtherCharges" type="xs:double" minOccurs="0" />
              <xs:element name="PreviousDue" type="xs:double" minOccurs="0" />
              <xs:element name="GrandTotal" type="xs:double" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>