Imports System.Data.SqlClient
Imports Microsoft.Reporting.WinForms

Public Class frmReportViewer
    Private Sub frmReportViewer_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.ReportViewer1.RefreshReport()
    End Sub

    ' أحداث أزرار شريط الأدوات
    Private Sub btnExportPDF_Click(sender As Object, e As EventArgs) Handles btnExportPDF.Click
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "PDF Files|*.pdf"
            saveDialog.Title = "حفظ التقرير كـ PDF"
            saveDialog.FileName = "Report_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".pdf"

            If saveDialog.ShowDialog() = DialogResult.OK Then
                ExportToPDF(saveDialog.FileName)
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في تصدير PDF: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnExportExcel_Click(sender As Object, e As EventArgs) Handles btnExportExcel.Click
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Excel Files|*.xls"
            saveDialog.Title = "حفظ التقرير كـ Excel"
            saveDialog.FileName = "Report_" & DateTime.Now.ToString("yyyyMMdd_HHmmss") & ".xls"

            If saveDialog.ShowDialog() = DialogResult.OK Then
                ExportToExcel(saveDialog.FileName)
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في تصدير Excel: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub btnPrint_Click(sender As Object, e As EventArgs) Handles btnPrint.Click
        PrintReport()
    End Sub

    Private Sub btnClose_Click(sender As Object, e As EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    ' دالة لعرض تقرير الفواتير
    Public Sub ShowInvoiceReport(invoiceNo As String, customerType As String)
        Try
            ReportViewer1.LocalReport.ReportPath = Application.StartupPath & "\Reports\InvoiceReport.rdlc"
            
            Dim ds As New DataSet()
            LoadInvoiceData(invoiceNo, ds)
            
            ' إضافة مصادر البيانات
            ReportViewer1.LocalReport.DataSources.Clear()
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("InvoiceDataSet", ds.Tables("InvoiceInfo")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("InvoiceProductDataSet", ds.Tables("Invoice_Product")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CustomerDataSet", ds.Tables("Customer")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CompanyDataSet", ds.Tables("Company")))
            
            ' إضافة المعاملات
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("InvoiceNo", invoiceNo))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            ReportViewer1.LocalReport.SetParameters(parameters)
            
            ReportViewer1.RefreshReport()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير المبيعات
    Public Sub ShowSalesReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            ReportViewer1.LocalReport.ReportPath = Application.StartupPath & "\Reports\SalesReport.rdlc"
            
            Dim ds As New DataSet()
            LoadSalesData(dateFrom, dateTo, ds)
            
            ReportViewer1.LocalReport.DataSources.Clear()
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("SalesDataSet", ds.Tables("Sales")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CompanyDataSet", ds.Tables("Company")))
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            ReportViewer1.LocalReport.SetParameters(parameters)
            
            ReportViewer1.RefreshReport()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير المشتريات
    Public Sub ShowPurchaseReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            ReportViewer1.LocalReport.ReportPath = Application.StartupPath & "\Reports\PurchaseReport.rdlc"
            
            Dim ds As New DataSet()
            LoadPurchaseData(dateFrom, dateTo, ds)
            
            ReportViewer1.LocalReport.DataSources.Clear()
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("PurchaseDataSet", ds.Tables("Purchase")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CompanyDataSet", ds.Tables("Company")))
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            ReportViewer1.LocalReport.SetParameters(parameters)
            
            ReportViewer1.RefreshReport()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير المخزون
    Public Sub ShowStockReport()
        Try
            ReportViewer1.LocalReport.ReportPath = Application.StartupPath & "\Reports\StockReport.rdlc"
            
            Dim ds As New DataSet()
            LoadStockData(ds)
            
            ReportViewer1.LocalReport.DataSources.Clear()
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("StockDataSet", ds.Tables("Stock")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CompanyDataSet", ds.Tables("Company")))
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            ReportViewer1.LocalReport.SetParameters(parameters)
            
            ReportViewer1.RefreshReport()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير العملاء
    Public Sub ShowCustomerReport()
        Try
            ReportViewer1.LocalReport.ReportPath = Application.StartupPath & "\Reports\CustomerReport.rdlc"
            
            Dim ds As New DataSet()
            LoadCustomerData(ds)
            
            ReportViewer1.LocalReport.DataSources.Clear()
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CustomerDataSet", ds.Tables("Customer")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CompanyDataSet", ds.Tables("Company")))
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            ReportViewer1.LocalReport.SetParameters(parameters)
            
            ReportViewer1.RefreshReport()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير الموردين
    Public Sub ShowSupplierReport()
        Try
            ReportViewer1.LocalReport.ReportPath = Application.StartupPath & "\Reports\SupplierReport.rdlc"
            
            Dim ds As New DataSet()
            LoadSupplierData(ds)
            
            ReportViewer1.LocalReport.DataSources.Clear()
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("SupplierDataSet", ds.Tables("Supplier")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CompanyDataSet", ds.Tables("Company")))
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            ReportViewer1.LocalReport.SetParameters(parameters)
            
            ReportViewer1.RefreshReport()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير الأرباح والخسائر
    Public Sub ShowProfitLossReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            ReportViewer1.LocalReport.ReportPath = Application.StartupPath & "\Reports\ProfitLossReport.rdlc"
            
            Dim ds As New DataSet()
            LoadProfitLossData(dateFrom, dateTo, ds)
            
            ReportViewer1.LocalReport.DataSources.Clear()
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("ProfitLossDataSet", ds.Tables("ProfitLoss")))
            ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource("CompanyDataSet", ds.Tables("Company")))
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            ReportViewer1.LocalReport.SetParameters(parameters)
            
            ReportViewer1.RefreshReport()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير عام مخصص
    Public Sub ShowCustomReport(reportPath As String, dataSet As DataSet, parameters As List(Of ReportParameter))
        Try
            ReportViewer1.LocalReport.ReportPath = reportPath

            ReportViewer1.LocalReport.DataSources.Clear()
            For Each table As DataTable In dataSet.Tables
                ReportViewer1.LocalReport.DataSources.Add(New ReportDataSource(table.TableName, table))
            Next

            If parameters IsNot Nothing AndAlso parameters.Count > 0 Then
                ReportViewer1.LocalReport.SetParameters(parameters)
            End If

            ReportViewer1.RefreshReport()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دوال تحميل البيانات
    Private Sub LoadInvoiceData(invoiceNo As String, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            ' تحميل بيانات الفاتورة
            Dim cmdInvoice As New SqlCommand("SELECT Customer.ID, Customer.Name, Customer.Gender, Customer.Address, Customer.City, Customer.State, Customer.ZipCode, Customer.ContactNo, Customer.EmailID, InvoiceInfo.Remarks, Customer.Photo, InvoiceInfo.Inv_ID, InvoiceInfo.InvoiceNo, InvoiceInfo.InvoiceDate, InvoiceInfo.CustomerID, InvoiceInfo.GrandTotal, InvoiceInfo.TotalPaid, InvoiceInfo.Balance FROM Customer INNER JOIN InvoiceInfo ON Customer.ID = InvoiceInfo.CustomerID WHERE InvoiceInfo.InvoiceNo = @invoiceNo", con)
            cmdInvoice.Parameters.AddWithValue("@invoiceNo", invoiceNo)
            Dim adpInvoice As New SqlDataAdapter(cmdInvoice)
            Dim dtInvoice As New DataTable("InvoiceInfo")
            adpInvoice.Fill(dtInvoice)
            ds.Tables.Add(dtInvoice)

            ' تحميل بيانات منتجات الفاتورة
            Dim cmdProducts As New SqlCommand("SELECT Invoice_Product.IPo_ID, Invoice_Product.InvoiceID, Invoice_Product.ProductID, Invoice_Product.CostPrice, Invoice_Product.SellingPrice, Invoice_Product.Margin, Invoice_Product.Qty, Invoice_Product.Amount, Invoice_Product.DiscountPer, Invoice_Product.Discount, Invoice_Product.VATPer, Invoice_Product.VAT, Invoice_Product.TotalAmount, Invoice_Product.Barcode, Product.PID, Product.ProductCode, Product.ProductName, Product.factor FROM Invoice_Product INNER JOIN Product ON Invoice_Product.ProductID = Product.PID INNER JOIN InvoiceInfo ON Invoice_Product.InvoiceID = InvoiceInfo.Inv_ID WHERE InvoiceInfo.InvoiceNo = @invoiceNo", con)
            cmdProducts.Parameters.AddWithValue("@invoiceNo", invoiceNo)
            Dim adpProducts As New SqlDataAdapter(cmdProducts)
            Dim dtProducts As New DataTable("Invoice_Product")
            adpProducts.Fill(dtProducts)
            ds.Tables.Add(dtProducts)

            ' تحميل بيانات العميل
            Dim cmdCustomer As New SqlCommand("SELECT Customer.* FROM Customer INNER JOIN InvoiceInfo ON Customer.ID = InvoiceInfo.CustomerID WHERE InvoiceInfo.InvoiceNo = @invoiceNo", con)
            cmdCustomer.Parameters.AddWithValue("@invoiceNo", invoiceNo)
            Dim adpCustomer As New SqlDataAdapter(cmdCustomer)
            Dim dtCustomer As New DataTable("Customer")
            adpCustomer.Fill(dtCustomer)
            ds.Tables.Add(dtCustomer)

            ' تحميل بيانات الشركة
            LoadCompanyData(ds)

            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Sub LoadSalesData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmdSales As New SqlCommand("SELECT InvoiceInfo.InvoiceNo, InvoiceInfo.InvoiceDate, Customer.Name AS CustomerName, InvoiceInfo.GrandTotal, InvoiceInfo.TotalPaid, InvoiceInfo.Balance FROM InvoiceInfo INNER JOIN Customer ON InvoiceInfo.CustomerID = Customer.ID WHERE InvoiceInfo.InvoiceDate BETWEEN @dateFrom AND @dateTo ORDER BY InvoiceInfo.InvoiceDate", con)
            cmdSales.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdSales.Parameters.AddWithValue("@dateTo", dateTo)
            Dim adpSales As New SqlDataAdapter(cmdSales)
            Dim dtSales As New DataTable("Sales")
            adpSales.Fill(dtSales)
            ds.Tables.Add(dtSales)

            LoadCompanyData(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Sub LoadPurchaseData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmdPurchase As New SqlCommand("SELECT PurchaseInfo.PurchaseNo, PurchaseInfo.PurchaseDate, Supplier.SupplierName, PurchaseInfo.GrandTotal FROM PurchaseInfo INNER JOIN Supplier ON PurchaseInfo.SupplierID = Supplier.SupplierID WHERE PurchaseInfo.PurchaseDate BETWEEN @dateFrom AND @dateTo ORDER BY PurchaseInfo.PurchaseDate", con)
            cmdPurchase.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdPurchase.Parameters.AddWithValue("@dateTo", dateTo)
            Dim adpPurchase As New SqlDataAdapter(cmdPurchase)
            Dim dtPurchase As New DataTable("Purchase")
            adpPurchase.Fill(dtPurchase)
            ds.Tables.Add(dtPurchase)

            LoadCompanyData(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Sub LoadStockData(ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmdStock As New SqlCommand("SELECT RTRIM(Product.ProductCode) AS ProductCode, RTRIM(ProductName) AS ProductName, RTRIM(Temp_Stock.Barcode) AS Barcode, CostPrice, SellingPrice, Discount, VAT, Qty FROM Temp_Stock INNER JOIN Product ON Product.PID = Temp_Stock.ProductID WHERE Qty > 0 ORDER BY ProductName", con)
            Dim adpStock As New SqlDataAdapter(cmdStock)
            Dim dtStock As New DataTable("Stock")
            adpStock.Fill(dtStock)
            ds.Tables.Add(dtStock)

            LoadCompanyData(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Sub LoadCustomerData(ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmdCustomer As New SqlCommand("SELECT CustomerID, Name, Gender, Address, City, State, ZipCode, ContactNo, EmailID, CustomerType FROM Customer ORDER BY Name", con)
            Dim adpCustomer As New SqlDataAdapter(cmdCustomer)
            Dim dtCustomer As New DataTable("Customer")
            adpCustomer.Fill(dtCustomer)
            ds.Tables.Add(dtCustomer)

            LoadCompanyData(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Sub LoadSupplierData(ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmdSupplier As New SqlCommand("SELECT SupplierID, SupplierName, Address, City, State, ZipCode, ContactNo, EmailID FROM Supplier ORDER BY SupplierName", con)
            Dim adpSupplier As New SqlDataAdapter(cmdSupplier)
            Dim dtSupplier As New DataTable("Supplier")
            adpSupplier.Fill(dtSupplier)
            ds.Tables.Add(dtSupplier)

            LoadCompanyData(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Sub LoadProfitLossData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            ' حساب إجمالي المبيعات
            Dim cmdSalesTotal As New SqlCommand("SELECT ISNULL(SUM(GrandTotal), 0) AS TotalSales FROM InvoiceInfo WHERE InvoiceDate BETWEEN @dateFrom AND @dateTo", con)
            cmdSalesTotal.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdSalesTotal.Parameters.AddWithValue("@dateTo", dateTo)

            ' حساب إجمالي المشتريات
            Dim cmdPurchaseTotal As New SqlCommand("SELECT ISNULL(SUM(GrandTotal), 0) AS TotalPurchases FROM PurchaseInfo WHERE PurchaseDate BETWEEN @dateFrom AND @dateTo", con)
            cmdPurchaseTotal.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdPurchaseTotal.Parameters.AddWithValue("@dateTo", dateTo)

            ' حساب إجمالي المصروفات
            Dim cmdExpensesTotal As New SqlCommand("SELECT ISNULL(SUM(Amount), 0) AS TotalExpenses FROM Voucher WHERE Date BETWEEN @dateFrom AND @dateTo", con)
            cmdExpensesTotal.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdExpensesTotal.Parameters.AddWithValue("@dateTo", dateTo)

            Dim totalSales As Decimal = Convert.ToDecimal(cmdSalesTotal.ExecuteScalar())
            Dim totalPurchases As Decimal = Convert.ToDecimal(cmdPurchaseTotal.ExecuteScalar())
            Dim totalExpenses As Decimal = Convert.ToDecimal(cmdExpensesTotal.ExecuteScalar())
            Dim netProfit As Decimal = totalSales - totalPurchases - totalExpenses

            ' إنشاء جدول الأرباح والخسائر
            Dim dtProfitLoss As New DataTable("ProfitLoss")
            dtProfitLoss.Columns.Add("Description", GetType(String))
            dtProfitLoss.Columns.Add("Amount", GetType(Decimal))

            dtProfitLoss.Rows.Add("إجمالي المبيعات", totalSales)
            dtProfitLoss.Rows.Add("إجمالي المشتريات", totalPurchases)
            dtProfitLoss.Rows.Add("إجمالي المصروفات", totalExpenses)
            dtProfitLoss.Rows.Add("صافي الربح", netProfit)

            ds.Tables.Add(dtProfitLoss)
            LoadCompanyData(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Sub LoadCompanyData(ds As DataSet)
        Try
            If con.State = ConnectionState.Closed Then
                con = New SqlConnection(cs)
                con.Open()
            End If

            Dim cmdCompany As New SqlCommand("SELECT * FROM Company", con)
            Dim adpCompany As New SqlDataAdapter(cmdCompany)
            Dim dtCompany As New DataTable("Company")
            adpCompany.Fill(dtCompany)

            ' التأكد من عدم وجود جدول الشركة مسبقاً
            If Not ds.Tables.Contains("Company") Then
                ds.Tables.Add(dtCompany)
            End If

        Catch ex As Exception
            Throw ex
        End Try
    End Sub

    ' دالة لتصدير التقرير إلى PDF
    Public Sub ExportToPDF(fileName As String)
        Try
            Dim warnings As Warning() = Nothing
            Dim streamids As String() = Nothing
            Dim mimeType As String = Nothing
            Dim encoding As String = Nothing
            Dim extension As String = Nothing

            Dim bytes As Byte() = ReportViewer1.LocalReport.Render("PDF", Nothing, mimeType, encoding, extension, streamids, warnings)

            Dim fs As New System.IO.FileStream(fileName, System.IO.FileMode.Create)
            fs.Write(bytes, 0, bytes.Length)
            fs.Close()

            MessageBox.Show("تم تصدير التقرير بنجاح إلى: " & fileName, "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لتصدير التقرير إلى Excel
    Public Sub ExportToExcel(fileName As String)
        Try
            Dim warnings As Warning() = Nothing
            Dim streamids As String() = Nothing
            Dim mimeType As String = Nothing
            Dim encoding As String = Nothing
            Dim extension As String = Nothing

            Dim bytes As Byte() = ReportViewer1.LocalReport.Render("Excel", Nothing, mimeType, encoding, extension, streamids, warnings)

            Dim fs As New System.IO.FileStream(fileName, System.IO.FileMode.Create)
            fs.Write(bytes, 0, bytes.Length)
            fs.Close()

            MessageBox.Show("تم تصدير التقرير بنجاح إلى: " & fileName, "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show("خطأ في تصدير التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لطباعة التقرير
    Public Sub PrintReport()
        Try
            ReportViewer1.PrintDialog()
        Catch ex As Exception
            MessageBox.Show("خطأ في طباعة التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Class
