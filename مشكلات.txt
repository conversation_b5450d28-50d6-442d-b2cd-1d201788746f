Message	1	The designer cannot process the code at line 1603: 

Me.btnKeyboard.Image = Global.Sales_and_Inventory_System.My.Resources.Resources.keyboard_icon__1_


The code within the method 'InitializeComponent' is generated by the designer and should not be manually modified.  Please remove any changes and try opening the designer again.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1604	0	
Warning	2	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	3	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	4	The primary reference "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	5	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	6	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	7	The primary reference "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	8	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	9	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	10	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	11	Could not resolve this reference. Could not locate the assembly "TouchlessLib". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.	Sales and Inventory System
Warning	12	Cannot get the file path for type library "237f4bec-8ae5-41e1-ae84-b194e4670597" version 13.0. Library not registered. (Exception from HRESULT: 0x8002801D (TYPE_E_LIBNOTREGISTERED))	Sales and Inventory System
Warning	13	The referenced component 'TouchlessLib' could not be found. 	Sales and Inventory System
Warning	14	The referenced component 'PAGEOBJECTMODELLib' could not be found. 	Sales and Inventory System
Error	15	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmAbout.Designer.vb	73	35	Sales and Inventory System
Error	16	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmActivation.Designer.vb	159	28	Sales and Inventory System
Error	17	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmChangePassword.Designer.vb	186	30	Sales and Inventory System
Error	18	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmChangePassword.Designer.vb	203	32	Sales and Inventory System
Error	19	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmCompany.Designer.vb	140	32	Sales and Inventory System
Error	20	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmCompany.vb	14	29	Sales and Inventory System
Error	21	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmCustomer.Designer.vb	247	28	Sales and Inventory System
Error	22	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmCustomer.vb	26	25	Sales and Inventory System
Error	23	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmCustomer.vb	380	25	Sales and Inventory System
Error	24	'ShowCreditorsReport' is not a member of 'Sales_and_Inventory_System.Sales_and_Inventory_System.ReportManager'.	D:\b Sales and Inventory System\frmDebtorsReport.vb	68	13	Sales and Inventory System
Error	25	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmGeneralLedger.vb	57	13	Sales and Inventory System
Error	26	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	152	35	Sales and Inventory System
Error	27	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	203	32	Sales and Inventory System
Error	28	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	241	30	Sales and Inventory System
Error	29	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	107	29	Sales and Inventory System
Error	30	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	217	29	Sales and Inventory System
Error	31	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	240	37	Sales and Inventory System
Error	32	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	173	45	Sales and Inventory System
Error	33	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	220	45	Sales and Inventory System
Error	34	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	229	39	Sales and Inventory System
Error	35	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	247	45	Sales and Inventory System
Error	36	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	256	45	Sales and Inventory System
Error	37	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	265	46	Sales and Inventory System
Error	38	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	274	47	Sales and Inventory System
Error	39	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	283	46	Sales and Inventory System
Error	40	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	293	45	Sales and Inventory System
Error	41	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	314	47	Sales and Inventory System
Error	42	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	350	45	Sales and Inventory System
Error	43	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	360	44	Sales and Inventory System
Error	44	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	430	45	Sales and Inventory System
Error	45	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	561	46	Sales and Inventory System
Error	46	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	582	42	Sales and Inventory System
Error	47	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	591	50	Sales and Inventory System
Error	48	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	609	39	Sales and Inventory System
Error	49	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	673	46	Sales and Inventory System
Error	50	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	681	49	Sales and Inventory System
Error	51	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	865	35	Sales and Inventory System
Error	52	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	881	29	Sales and Inventory System
Error	53	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1604	32	Sales and Inventory System
Error	54	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1622	32	Sales and Inventory System
Error	55	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1640	32	Sales and Inventory System
Error	56	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1657	29	Sales and Inventory System
Error	57	'Sales_and_Inventory_System' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPOS.vb	531	44	Sales and Inventory System
Error	58	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPOS.vb	1127	33	Sales and Inventory System
Error	59	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPOS.vb	1130	33	Sales and Inventory System
Error	60	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPayment.Designer.vb	167	29	Sales and Inventory System
Error	61	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPayment_2.Designer.vb	283	29	Sales and Inventory System
Error	62	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPayment_3.Designer.vb	277	29	Sales and Inventory System
Error	63	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmProduct.Designer.vb	376	28	Sales and Inventory System
Error	64	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmProduct.vb	73	25	Sales and Inventory System
Error	65	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmProduct.vb	528	25	Sales and Inventory System
Error	66	'ShowProfitLossReport' is not a member of 'Sales_and_Inventory_System.Sales_and_Inventory_System.ReportManager'.	D:\b Sales and Inventory System\frmProfitAndLossReport.vb	48	13	Sales and Inventory System
Error	67	'ShowSupplierPurchaseReport' is not a member of 'Sales_and_Inventory_System.Sales_and_Inventory_System.ReportManager'.	D:\b Sales and Inventory System\frmPurchaseReport.vb	30	13	Sales and Inventory System
Error	68	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmPurchaseReturn.Designer.vb	675	29	Sales and Inventory System
Error	69	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmQuotation.vb	455	44	Sales and Inventory System
Error	70	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmRecoveryPassword.Designer.vb	111	30	Sales and Inventory System
Error	71	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmRecoveryPassword.Designer.vb	129	32	Sales and Inventory System
Error	72	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmSalesReturn.Designer.vb	1048	29	Sales and Inventory System
Error	73	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmSalesman.Designer.vb	202	28	Sales and Inventory System
Error	74	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmSalesman.vb	23	25	Sales and Inventory System
Error	75	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmSalesman.vb	304	25	Sales and Inventory System
Error	76	Argument not specified for parameter 'dateTo' of 'Public Shared Sub ShowSalesmanCommissionReport(salesmanId As String, dateFrom As Date, dateTo As Date)'.	D:\b Sales and Inventory System\frmSalesmanCommissionReport.vb	55	13	Sales and Inventory System
Error	77	'SetDataSource' is not a member of 'Sales_and_Inventory_System.rptSalesmanLedger'.	D:\b Sales and Inventory System\frmSalesmanLedger.vb	87	13	Sales and Inventory System
Error	78	'SetParameterValue' is not a member of 'Sales_and_Inventory_System.rptSalesmanLedger'.	D:\b Sales and Inventory System\frmSalesmanLedger.vb	88	13	Sales and Inventory System
Error	79	'SetParameterValue' is not a member of 'Sales_and_Inventory_System.rptSalesmanLedger'.	D:\b Sales and Inventory System\frmSalesmanLedger.vb	89	13	Sales and Inventory System
Error	80	'CrystalReportViewer1' is not a member of 'Sales_and_Inventory_System.frmReport'.	D:\b Sales and Inventory System\frmSalesmanLedger.vb	90	13	Sales and Inventory System
Error	81	'SetDataSource' is not a member of 'Sales_and_Inventory_System.rptSalesmanLedger_2'.	D:\b Sales and Inventory System\frmSalesmanLedger.vb	139	13	Sales and Inventory System
Error	82	'SetParameterValue' is not a member of 'Sales_and_Inventory_System.rptSalesmanLedger_2'.	D:\b Sales and Inventory System\frmSalesmanLedger.vb	140	13	Sales and Inventory System
Error	83	'SetParameterValue' is not a member of 'Sales_and_Inventory_System.rptSalesmanLedger_2'.	D:\b Sales and Inventory System\frmSalesmanLedger.vb	141	13	Sales and Inventory System
Error	84	'CrystalReportViewer1' is not a member of 'Sales_and_Inventory_System.frmReport'.	D:\b Sales and Inventory System\frmSalesmanLedger.vb	142	13	Sales and Inventory System
Error	85	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmSplash.Designer.vb	119	32	Sales and Inventory System
Error	86	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmSplash.Designer.vb	142	30	Sales and Inventory System
Error	87	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmSqlServerSetting.Designer.vb	88	38	Sales and Inventory System
Error	88	Argument not specified for parameter 'dateFrom' of 'Public Shared Sub ShowStockInReport(dateFrom As Date, dateTo As Date)'.	D:\b Sales and Inventory System\frmStockInAndOutReport.vb	18	13	Sales and Inventory System
Error	89	Argument not specified for parameter 'dateTo' of 'Public Shared Sub ShowStockInReport(dateFrom As Date, dateTo As Date)'.	D:\b Sales and Inventory System\frmStockInAndOutReport.vb	18	13	Sales and Inventory System
Error	90	'ShowStockOutReport' is not a member of 'Sales_and_Inventory_System.Sales_and_Inventory_System.ReportManager'.	D:\b Sales and Inventory System\frmStockInAndOutReport.vb	27	13	Sales and Inventory System
Error	91	'My' is not a member of 'Sales_and_Inventory_System'.	D:\b Sales and Inventory System\frmSupplier.Designer.vb	121	29	Sales and Inventory System
Error	92	'ShowServiceTaxReport' is not a member of 'Sales_and_Inventory_System.Sales_and_Inventory_System.ReportManager'.	D:\b Sales and Inventory System\frmTaxReport.vb	92	13	Sales and Inventory System
Warning	93	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditors.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	94	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTerms.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	95	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTermsStatements.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	96	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	97	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales1.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	98	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales2.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	99	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesmanLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	100	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptService.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	101	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptStockOut.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	102	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTpurchases.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	103	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTservice.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
