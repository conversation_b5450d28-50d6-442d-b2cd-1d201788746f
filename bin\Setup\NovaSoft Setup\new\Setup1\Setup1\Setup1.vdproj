﻿"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{978C614F-708E-4E1A-B201-565925725DBA}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:Setup1"
"LanguageId" = "3:1033"
"CodePage" = "3:1252"
"UILanguageId" = "3:1033"
"SccProjectName" = "8:"
"SccLocalPath" = "8:"
"SccAuxPath" = "8:"
"SccProvider" = "8:"
    "Hierarchy"
    {
        "Entry"
        {
        "MsmKey" = "8:_006F99A14633DBE849890C733495FC59"
        "OwnerKey" = "8:_97E5E5EDFC3441BDAE86DF6853D37EB6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_04694B56F682E55203F62AA9D0497603"
        "OwnerKey" = "8:_3B3F4A4088414BFBB9C918FBB8033456"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_04F4F2655855318E3BDCF9B95C447F65"
        "OwnerKey" = "8:_FC2B6339F1E6484C914F04FE30B00D10"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_04F4F2655855318E3BDCF9B95C447F65"
        "OwnerKey" = "8:_C3E630EF5CAB42BD8E1D1C11333336B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_04F4F2655855318E3BDCF9B95C447F65"
        "OwnerKey" = "8:_C523BB6A4EEC714D48AFFD7864827832"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_04F4F2655855318E3BDCF9B95C447F65"
        "OwnerKey" = "8:_5D28704BD2FFB8F9CFFBE49FB0B98397"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_04F4F2655855318E3BDCF9B95C447F65"
        "OwnerKey" = "8:_EAD867ED1F2B4039B965812C509147AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_06EF4EFAAE304237BB26178ECA4E3DD2"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0728FAFEF7184032AA0F4BB48319024C"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0931D6237FD64364BAA50CAFE69B1003"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0AAB85A9754548858DDB9F662A104806"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0C58EADC2BD14C98A017CE92040F2013"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_12077EFA8FF84EE2A321BA83C93D07E7"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_159FE3A36E5B23A9A44043C34B4D89A2"
        "OwnerKey" = "8:_DBFC1AF59F0D4513BBF79314E5E76499"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_159FE3A36E5B23A9A44043C34B4D89A2"
        "OwnerKey" = "8:_4CFABD14277E488CBCC3AEC44E53D09D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_159FE3A36E5B23A9A44043C34B4D89A2"
        "OwnerKey" = "8:_D1716580D20F406F884EA3D9BD037A7D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_159FE3A36E5B23A9A44043C34B4D89A2"
        "OwnerKey" = "8:_E00F1C275B9D2D602C6CA9FBC2C9A7F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_159FE3A36E5B23A9A44043C34B4D89A2"
        "OwnerKey" = "8:_DA4E9AC6822E40B8AC92BBF4C1F9A9BA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_159FE3A36E5B23A9A44043C34B4D89A2"
        "OwnerKey" = "8:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_174B0B0A34674A839C96982561BA48C7"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
        "OwnerKey" = "8:_DBFC1AF59F0D4513BBF79314E5E76499"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
        "OwnerKey" = "8:_4CFABD14277E488CBCC3AEC44E53D09D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
        "OwnerKey" = "8:_D1716580D20F406F884EA3D9BD037A7D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
        "OwnerKey" = "8:_E00F1C275B9D2D602C6CA9FBC2C9A7F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
        "OwnerKey" = "8:_DA4E9AC6822E40B8AC92BBF4C1F9A9BA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1D2EDBDC5CD64C7CA81107C5F0F955F8"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_265BD92CA68E4863A95BF00506D8B992"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "OwnerKey" = "8:_12077EFA8FF84EE2A321BA83C93D07E7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "OwnerKey" = "8:_88BE5F9BC6FEBC1ED6F1E453AF0A930F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "OwnerKey" = "8:_D55E489FFB674D2B8C5D765ADC313BDC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "OwnerKey" = "8:_8080493E752948ADADF8F2057A5B34EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "OwnerKey" = "8:_9E3FA0ADB406437DAFD05F25C7C0F761"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "OwnerKey" = "8:_7B1B4987EA4144B2AD6AD864E5ACC061"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28A8A88D7CB65EF6E71FCF1E67F8C726"
        "OwnerKey" = "8:_159FE3A36E5B23A9A44043C34B4D89A2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_28A8A88D7CB65EF6E71FCF1E67F8C726"
        "OwnerKey" = "8:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2B36642434454906A983A7C8AA61CD13"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2FEAF5AD67C74E59B9F6BF3D64067C59"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_306D43AF83C270A4119119D69F5E8C89"
        "OwnerKey" = "8:_DBFC1AF59F0D4513BBF79314E5E76499"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_306D43AF83C270A4119119D69F5E8C89"
        "OwnerKey" = "8:_E00F1C275B9D2D602C6CA9FBC2C9A7F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_306D43AF83C270A4119119D69F5E8C89"
        "OwnerKey" = "8:_DA4E9AC6822E40B8AC92BBF4C1F9A9BA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_30C455EC53EE45C59902C9B0A273A6A1"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_331C3666AF8F85D76A0824E3713F7F4A"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_33865ED24F1D4AD690AD7A831D89D9B3"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_338F58E3177449FFACE228D49A75E6E3"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_349FF579D78745DDAD0D11443CD71859"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_34B27B4E41BB4121A0B4B06B083CB8AD"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_353D608B1AC3445ABB3E1294809ADE93"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3B3F4A4088414BFBB9C918FBB8033456"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3EBB2AE03BD6403FBEE8D8C08DB8CF83"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4A0ADC746D58463986F72A92C9975571"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4CFABD14277E488CBCC3AEC44E53D09D"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5D28704BD2FFB8F9CFFBE49FB0B98397"
        "OwnerKey" = "8:_006F99A14633DBE849890C733495FC59"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5D28704BD2FFB8F9CFFBE49FB0B98397"
        "OwnerKey" = "8:_F41DBE7D07BA4A7F9274CF2D8371032D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DED75FADEB83C189C89A18A1BBF4D1C"
        "OwnerKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DED75FADEB83C189C89A18A1BBF4D1C"
        "OwnerKey" = "8:_D3C31D8012244B40912FF7D8107DE01F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DED75FADEB83C189C89A18A1BBF4D1C"
        "OwnerKey" = "8:_33865ED24F1D4AD690AD7A831D89D9B3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DED75FADEB83C189C89A18A1BBF4D1C"
        "OwnerKey" = "8:_9626213AA0D3410DA87C771F55717B5C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DED75FADEB83C189C89A18A1BBF4D1C"
        "OwnerKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DED75FADEB83C189C89A18A1BBF4D1C"
        "OwnerKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_611761EC1CABB58FEE4E13A0C7E218E7"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_637AF8D014BD4EBB817B27FC97ABD6D9"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_653C6B639066452B8D5A836036A3A6B7"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_66F859787547AB874CC39440F2218BF2"
        "OwnerKey" = "8:_265BD92CA68E4863A95BF00506D8B992"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_68439789BABCFF271828495D57236D60"
        "OwnerKey" = "8:_8080493E752948ADADF8F2057A5B34EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_684854A22EEF4411850F59466B530D49"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_697DD865838F47FE8EE2A438CB99149B"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_69F7B2743D6B4662ABE755D73BD5278F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6BDA0A061D0646819C05E3689364CE8C"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_71D1457540B24A6297EB19DC578A5500"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_76CF2E3B5FCB4F8D9C074630112092B3"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7ACC1127811C896EF1EA2B674633E521"
        "OwnerKey" = "8:_12077EFA8FF84EE2A321BA83C93D07E7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7B1B4987EA4144B2AD6AD864E5ACC061"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7C21ACFFF3094DE79C73D11AB4CF8B8F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8080493E752948ADADF8F2057A5B34EE"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81A84896E71C1DBE275B03924AA6E9D9"
        "OwnerKey" = "8:_D55E489FFB674D2B8C5D765ADC313BDC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_12077EFA8FF84EE2A321BA83C93D07E7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_88BE5F9BC6FEBC1ED6F1E453AF0A930F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_D55E489FFB674D2B8C5D765ADC313BDC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_D3C31D8012244B40912FF7D8107DE01F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_68439789BABCFF271828495D57236D60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_8080493E752948ADADF8F2057A5B34EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_BE2BB22BDF3940FF8B24E9440FAE2DD0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_DB6298CA6DFB497499E6325C8FDF7488"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_0C58EADC2BD14C98A017CE92040F2013"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_331C3666AF8F85D76A0824E3713F7F4A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_33865ED24F1D4AD690AD7A831D89D9B3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_684854A22EEF4411850F59466B530D49"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_0931D6237FD64364BAA50CAFE69B1003"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_9E3FA0ADB406437DAFD05F25C7C0F761"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_7B1B4987EA4144B2AD6AD864E5ACC061"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_6BDA0A061D0646819C05E3689364CE8C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_CB86E9CEDDB7DBA42B692D24FD942BB1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_A435C5538D47D75814645327E81B307C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "OwnerKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_882356BF36AE4C5184925BFE45ADF546"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_88BE5F9BC6FEBC1ED6F1E453AF0A930F"
        "OwnerKey" = "8:_637AF8D014BD4EBB817B27FC97ABD6D9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8B0DFECCC44449E59CDA4BCCE15CF979"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8E29E048778F5DAE8BABC8A618D2FD86"
        "OwnerKey" = "8:_7B1B4987EA4144B2AD6AD864E5ACC061"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9237AD3867644961BF103193416FDBAF"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_934D3C8A3D0B4DE2ADACE68D582D6C9A"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_934DFDC88CC104415AFE14387F9EF299"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_93E05772AB298F1A6965DBF92B12AB9B"
        "OwnerKey" = "8:_A435C5538D47D75814645327E81B307C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_93E05772AB298F1A6965DBF92B12AB9B"
        "OwnerKey" = "8:_DB6298CA6DFB497499E6325C8FDF7488"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_93E05772AB298F1A6965DBF92B12AB9B"
        "OwnerKey" = "8:_934DFDC88CC104415AFE14387F9EF299"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_93E05772AB298F1A6965DBF92B12AB9B"
        "OwnerKey" = "8:_9E3FA0ADB406437DAFD05F25C7C0F761"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_94C06A49CA6045DAACC10D100CC16DEA"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9626213AA0D3410DA87C771F55717B5C"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_96BA709D0B2A47C69BC0A1A1419524E1"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97E5E5EDFC3441BDAE86DF6853D37EB6"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_997B083E644D4DC29875A9D527068B73"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9C3C6F5BAA4344A8A03D1F918B90C12A"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9E3FA0ADB406437DAFD05F25C7C0F761"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A435C5538D47D75814645327E81B307C"
        "OwnerKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A435C5538D47D75814645327E81B307C"
        "OwnerKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A435C5538D47D75814645327E81B307C"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A435C5538D47D75814645327E81B307C"
        "OwnerKey" = "8:_1D2EDBDC5CD64C7CA81107C5F0F955F8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A435C5538D47D75814645327E81B307C"
        "OwnerKey" = "8:_7B1B4987EA4144B2AD6AD864E5ACC061"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A549852092D64A39B75968A69A1E3F73"
        "OwnerKey" = "8:_CB86E9CEDDB7DBA42B692D24FD942BB1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A549852092D64A39B75968A69A1E3F73"
        "OwnerKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A549852092D64A39B75968A69A1E3F73"
        "OwnerKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A911F0257BDB43CD8EE84DB1C541B1F2"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B4FD6F6F8F18442697B01757B8E9AEA3"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B6D94AF8D02448AFB6E61059A4A3D2CA"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B9463F35184A447EBC601AFF338649E6"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BE2BB22BDF3940FF8B24E9440FAE2DD0"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BF72CFAFDFB64D7A854AEE9FA6BDA545"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C0CBE63A65DC41749B72CD508D7F0983"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1E0BDB4670A4B3690E67D24405F5FF5"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C3E630EF5CAB42BD8E1D1C11333336B6"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C523BB6A4EEC714D48AFFD7864827832"
        "OwnerKey" = "8:_CA1E889B435F198E99D6A977B800EDF5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C523BB6A4EEC714D48AFFD7864827832"
        "OwnerKey" = "8:_0728FAFEF7184032AA0F4BB48319024C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C5CE8785C4C74FF3AD5BD2DA886A2866"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C7AED02260584C28A7F06E761BA3F670"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CA1E889B435F198E99D6A977B800EDF5"
        "OwnerKey" = "8:_0728FAFEF7184032AA0F4BB48319024C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CB86E9CEDDB7DBA42B692D24FD942BB1"
        "OwnerKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CB86E9CEDDB7DBA42B692D24FD942BB1"
        "OwnerKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D06404817A0D486F9B78A650EC25064E"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D1716580D20F406F884EA3D9BD037A7D"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D3C31D8012244B40912FF7D8107DE01F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D55E489FFB674D2B8C5D765ADC313BDC"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D773825E0C0B46A4AC54158C7BABF8D5"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D778102E53E64A9CA00EB2D9E6DF64CD"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D7F5B31BBFFB42879EC7FBAE2340CD68"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DA4E9AC6822E40B8AC92BBF4C1F9A9BA"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DB6298CA6DFB497499E6325C8FDF7488"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DBFC1AF59F0D4513BBF79314E5E76499"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DCA61F7BD1EA47443F4BE8888BAEA92A"
        "OwnerKey" = "8:_5D28704BD2FFB8F9CFFBE49FB0B98397"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DCA61F7BD1EA47443F4BE8888BAEA92A"
        "OwnerKey" = "8:_C3E630EF5CAB42BD8E1D1C11333336B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DDFBA09E7999406BAF02DE4AAEBD336F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_A435C5538D47D75814645327E81B307C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_88BE5F9BC6FEBC1ED6F1E453AF0A930F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_637AF8D014BD4EBB817B27FC97ABD6D9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_81A84896E71C1DBE275B03924AA6E9D9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_D55E489FFB674D2B8C5D765ADC313BDC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_D3C31D8012244B40912FF7D8107DE01F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_68439789BABCFF271828495D57236D60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_8080493E752948ADADF8F2057A5B34EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_BE2BB22BDF3940FF8B24E9440FAE2DD0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_DB6298CA6DFB497499E6325C8FDF7488"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_0C58EADC2BD14C98A017CE92040F2013"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_331C3666AF8F85D76A0824E3713F7F4A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_684854A22EEF4411850F59466B530D49"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_0931D6237FD64364BAA50CAFE69B1003"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_9E3FA0ADB406437DAFD05F25C7C0F761"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_7B1B4987EA4144B2AD6AD864E5ACC061"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_CB86E9CEDDB7DBA42B692D24FD942BB1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "OwnerKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E00F1C275B9D2D602C6CA9FBC2C9A7F5"
        "OwnerKey" = "8:_DA4E9AC6822E40B8AC92BBF4C1F9A9BA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E31767B763F34B61A36A0B783AF79E38"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E4CF529E388B440CB7EEE650370228C9"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E8E300C4F3824363849CE1DA63AA851F"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EAD867ED1F2B4039B965812C509147AF"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EE59B4FD326A4E529DDF8B7A22ADB4FB"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F41DBE7D07BA4A7F9274CF2D8371032D"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FC2B6339F1E6484C914F04FE30B00D10"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FC8576C493CA8ECBDEAE984B695EF06D"
        "OwnerKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FC8576C493CA8ECBDEAE984B695EF06D"
        "OwnerKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_A435C5538D47D75814645327E81B307C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_8080493E752948ADADF8F2057A5B34EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_30C455EC53EE45C59902C9B0A273A6A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_9E3FA0ADB406437DAFD05F25C7C0F761"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_FC8576C493CA8ECBDEAE984B695EF06D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "OwnerKey" = "8:_CB86E9CEDDB7DBA42B692D24FD942BB1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5DED75FADEB83C189C89A18A1BBF4D1C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EE59B4FD326A4E529DDF8B7A22ADB4FB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D778102E53E64A9CA00EB2D9E6DF64CD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C3E630EF5CAB42BD8E1D1C11333336B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_637AF8D014BD4EBB817B27FC97ABD6D9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_88BE5F9BC6FEBC1ED6F1E453AF0A930F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_34B27B4E41BB4121A0B4B06B083CB8AD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D55E489FFB674D2B8C5D765ADC313BDC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_81A84896E71C1DBE275B03924AA6E9D9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D3C31D8012244B40912FF7D8107DE01F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8080493E752948ADADF8F2057A5B34EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_68439789BABCFF271828495D57236D60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E52B23B60E6A4BF589EDE77A4991E04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_30C455EC53EE45C59902C9B0A273A6A1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BE2BB22BDF3940FF8B24E9440FAE2DD0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DB6298CA6DFB497499E6325C8FDF7488"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4CFABD14277E488CBCC3AEC44E53D09D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D1716580D20F406F884EA3D9BD037A7D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DA4E9AC6822E40B8AC92BBF4C1F9A9BA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E00F1C275B9D2D602C6CA9FBC2C9A7F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DBFC1AF59F0D4513BBF79314E5E76499"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_306D43AF83C270A4119119D69F5E8C89"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_159FE3A36E5B23A9A44043C34B4D89A2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_28A8A88D7CB65EF6E71FCF1E67F8C726"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0C58EADC2BD14C98A017CE92040F2013"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_596CFAEB56B94B45AD2958FD0B5E2B73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_611761EC1CABB58FEE4E13A0C7E218E7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_934DFDC88CC104415AFE14387F9EF299"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_331C3666AF8F85D76A0824E3713F7F4A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_33865ED24F1D4AD690AD7A831D89D9B3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_684854A22EEF4411850F59466B530D49"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0931D6237FD64364BAA50CAFE69B1003"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1D2EDBDC5CD64C7CA81107C5F0F955F8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9E3FA0ADB406437DAFD05F25C7C0F761"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7B1B4987EA4144B2AD6AD864E5ACC061"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8E29E048778F5DAE8BABC8A618D2FD86"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6BDA0A061D0646819C05E3689364CE8C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9626213AA0D3410DA87C771F55717B5C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_882356BF36AE4C5184925BFE45ADF546"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0728FAFEF7184032AA0F4BB48319024C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CA1E889B435F198E99D6A977B800EDF5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C523BB6A4EEC714D48AFFD7864827832"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F41DBE7D07BA4A7F9274CF2D8371032D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_97E5E5EDFC3441BDAE86DF6853D37EB6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_006F99A14633DBE849890C733495FC59"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5D28704BD2FFB8F9CFFBE49FB0B98397"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3B3F4A4088414BFBB9C918FBB8033456"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_04694B56F682E55203F62AA9D0497603"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E4CF529E388B440CB7EEE650370228C9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C7AED02260584C28A7F06E761BA3F670"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EAD867ED1F2B4039B965812C509147AF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9237AD3867644961BF103193416FDBAF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B4FD6F6F8F18442697B01757B8E9AEA3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_338F58E3177449FFACE228D49A75E6E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_653C6B639066452B8D5A836036A3A6B7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_174B0B0A34674A839C96982561BA48C7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9C3C6F5BAA4344A8A03D1F918B90C12A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C1E0BDB4670A4B3690E67D24405F5FF5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FC2B6339F1E6484C914F04FE30B00D10"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_04F4F2655855318E3BDCF9B95C447F65"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_94C06A49CA6045DAACC10D100CC16DEA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_69F7B2743D6B4662ABE755D73BD5278F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_265BD92CA68E4863A95BF00506D8B992"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_66F859787547AB874CC39440F2218BF2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FE15720A0F094DAF559F7EE25DF97070"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FC8576C493CA8ECBDEAE984B695EF06D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DE7524DA993AB5C6E729AF8D26078E71"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CB86E9CEDDB7DBA42B692D24FD942BB1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A549852092D64A39B75968A69A1E3F73"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A435C5538D47D75814645327E81B307C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_93E05772AB298F1A6965DBF92B12AB9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_81DA515F1B87EEB1A69CD7396B95B04A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_284683610E0549DFAE28090B559F02EF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_12077EFA8FF84EE2A321BA83C93D07E7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7ACC1127811C896EF1EA2B674633E521"
        "MsmSig" = "8:_UNDEFINED"
        }
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:Debug\\Setup1.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:.NETFramework,Version=v4.0,Profile=Client"
                    {
                    "Name" = "8:Microsoft .NET Framework 4 Client Profile (x86 and x64)"
                    "ProductCode" = "8:.NETFramework,Version=v4.0,Profile=Client"
                    }
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:Microsoft.Windows.Installer.3.1"
                    {
                    "Name" = "8:Windows Installer 3.1"
                    "ProductCode" = "8:Microsoft.Windows.Installer.3.1"
                    }
                }
            }
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:Release\\Setup1.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
            }
        }
    }
    "Deployable"
    {
        "CustomAction"
        {
        }
        "DefaultFeature"
        {
        "Name" = "8:DefaultFeature"
        "Title" = "8:"
        "Description" = "8:"
        }
        "ExternalPersistence"
        {
            "LaunchCondition"
            {
                "{A06ECF26-33A3-4562-8140-9B0E340D4F24}:_CAB58F13BB46453DBB5F0E497A9AA8B4"
                {
                "Name" = "8:.NET Framework"
                "Message" = "8:[VSDNETMSG]"
                "FrameworkVersion" = "8:.NETFramework,Version=v4.0,Profile=Client"
                "AllowLaterVersions" = "11:FALSE"
                "InstallUrl" = "8:http://go.microsoft.com/fwlink/?LinkId=131000"
                }
            }
        }
        "File"
        {
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_006F99A14633DBE849890C733495FC59"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:FlashControlV71, Version=1.0.3187.32366, Culture=neutral, PublicKeyToken=692fbea5521e1304"
                "ScatterAssemblies"
                {
                    "_006F99A14633DBE849890C733495FC59"
                    {
                    "Name" = "8:FlashControlV71.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:FlashControlV71.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_04694B56F682E55203F62AA9D0497603"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:log4net, Version=1.2.10.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_04694B56F682E55203F62AA9D0497603"
                    {
                    "Name" = "8:log4net.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:log4net.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_04F4F2655855318E3BDCF9B95C447F65"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_04F4F2655855318E3BDCF9B95C447F65"
                    {
                    "Name" = "8:stdole.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:stdole.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_06EF4EFAAE304237BB26178ECA4E3DD2"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\TotalPurchase.xml"
            "TargetName" = "8:TotalPurchase.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0728FAFEF7184032AA0F4BB48319024C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Office.Interop.Excel, Version=14.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0728FAFEF7184032AA0F4BB48319024C"
                    {
                    "Name" = "8:Microsoft.Office.Interop.Excel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.Office.Interop.Excel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0931D6237FD64364BAA50CAFE69B1003"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Collector, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0931D6237FD64364BAA50CAFE69B1003"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Collector.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.Collector.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_0AAB85A9754548858DDB9F662A104806"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Creditors.xml"
            "TargetName" = "8:Creditors.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0C58EADC2BD14C98A017CE92040F2013"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.UtilityEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_0C58EADC2BD14C98A017CE92040F2013"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.UtilityEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.UtilityEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_12077EFA8FF84EE2A321BA83C93D07E7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Sales and Inventory System, Version=4.0.1.0, Culture=neutral, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_12077EFA8FF84EE2A321BA83C93D07E7"
                    {
                    "Name" = "8:Sales and Inventory System.exe"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Sales and Inventory System.exe"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_159FE3A36E5B23A9A44043C34B4D89A2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.ConnectionInfo, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_159FE3A36E5B23A9A44043C34B4D89A2"
                    {
                    "Name" = "8:Microsoft.SqlServer.ConnectionInfo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.ConnectionInfo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_174B0B0A34674A839C96982561BA48C7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.CubeDefModel, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_174B0B0A34674A839C96982561BA48C7"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.CubeDefModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.CubeDefModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Sdk.Sfc, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_1A9EDB9D2E9198DC1885BAC4FEAE99DC"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Sdk.Sfc.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Sdk.Sfc.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1D2EDBDC5CD64C7CA81107C5F0F955F8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.DmfSqlClrWrapper, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_1D2EDBDC5CD64C7CA81107C5F0F955F8"
                    {
                    "Name" = "8:Microsoft.SqlServer.DmfSqlClrWrapper.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.DmfSqlClrWrapper.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_265BD92CA68E4863A95BF00506D8B992"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.CrystalReports.Engine, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_265BD92CA68E4863A95BF00506D8B992"
                    {
                    "Name" = "8:CrystalDecisions.CrystalReports.Engine.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.CrystalReports.Engine.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_284683610E0549DFAE28090B559F02EF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Smo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_284683610E0549DFAE28090B559F02EF"
                    {
                    "Name" = "8:Microsoft.SqlServer.Smo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Smo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_28A8A88D7CB65EF6E71FCF1E67F8C726"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlClrProvider, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_28A8A88D7CB65EF6E71FCF1E67F8C726"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlClrProvider.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlClrProvider.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_2B36642434454906A983A7C8AA61CD13"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Sales and Inventory System.xml"
            "TargetName" = "8:Sales and Inventory System.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_2FEAF5AD67C74E59B9F6BF3D64067C59"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\StockOut.xml"
            "TargetName" = "8:StockOut.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_306D43AF83C270A4119119D69F5E8C89"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Diagnostics.STrace, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_306D43AF83C270A4119119D69F5E8C89"
                    {
                    "Name" = "8:Microsoft.SqlServer.Diagnostics.STrace.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Diagnostics.STrace.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_30C455EC53EE45C59902C9B0A273A6A1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.ServiceBrokerEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_30C455EC53EE45C59902C9B0A273A6A1"
                    {
                    "Name" = "8:Microsoft.SqlServer.ServiceBrokerEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.ServiceBrokerEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_331C3666AF8F85D76A0824E3713F7F4A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Collector, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_331C3666AF8F85D76A0824E3713F7F4A"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Collector.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Collector.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_33865ED24F1D4AD690AD7A831D89D9B3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Sdk.Sfc, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_33865ED24F1D4AD690AD7A831D89D9B3"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Sdk.Sfc.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.Sdk.Sfc.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_338F58E3177449FFACE228D49A75E6E3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.DataSetConversion, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_338F58E3177449FFACE228D49A75E6E3"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.DataSetConversion.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.DataSetConversion.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_349FF579D78745DDAD0D11443CD71859"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\IDAutomationHC39M.ttf"
            "TargetName" = "8:IDAutomationHC39M.ttf"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_34B27B4E41BB4121A0B4B06B083CB8AD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SString, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_34B27B4E41BB4121A0B4B06B083CB8AD"
                    {
                    "Name" = "8:Microsoft.SqlServer.SString.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.SString.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_353D608B1AC3445ABB3E1294809ADE93"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\fevicon.ico"
            "TargetName" = "8:fevicon.ico"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3B3F4A4088414BFBB9C918FBB8033456"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.Shared, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_3B3F4A4088414BFBB9C918FBB8033456"
                    {
                    "Name" = "8:CrystalDecisions.Shared.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.Shared.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_3EBB2AE03BD6403FBEE8D8C08DB8CF83"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\BarcodeLabelPrinting.xml"
            "TargetName" = "8:BarcodeLabelPrinting.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_4A0ADC746D58463986F72A92C9975571"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\TotalSales.xml"
            "TargetName" = "8:TotalSales.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4CFABD14277E488CBCC3AEC44E53D09D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.XEventEnum, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4CFABD14277E488CBCC3AEC44E53D09D"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.XEventEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.XEventEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_596CFAEB56B94B45AD2958FD0B5E2B73"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Utility, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_596CFAEB56B94B45AD2958FD0B5E2B73"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Utility.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.Utility.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5D28704BD2FFB8F9CFFBE49FB0B98397"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:ShockwaveFlashObjects, Version=1.0.0.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
                "ScatterAssemblies"
                {
                    "_5D28704BD2FFB8F9CFFBE49FB0B98397"
                    {
                    "Name" = "8:ShockwaveFlashObjects.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:ShockwaveFlashObjects.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5DED75FADEB83C189C89A18A1BBF4D1C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlClrProvider, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_5DED75FADEB83C189C89A18A1BBF4D1C"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlClrProvider.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlClrProvider.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_611761EC1CABB58FEE4E13A0C7E218E7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SString, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_611761EC1CABB58FEE4E13A0C7E218E7"
                    {
                    "Name" = "8:Microsoft.SqlServer.SString.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SString.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_637AF8D014BD4EBB817B27FC97ABD6D9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.WmiEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_637AF8D014BD4EBB817B27FC97ABD6D9"
                    {
                    "Name" = "8:Microsoft.SqlServer.WmiEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.WmiEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_653C6B639066452B8D5A836036A3A6B7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.DataDefModel, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_653C6B639066452B8D5A836036A3A6B7"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.DataDefModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.DataDefModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_66F859787547AB874CC39440F2218BF2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.Prompting, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_66F859787547AB874CC39440F2218BF2"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.Prompting.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CrystalDecisions.ReportAppServer.Prompting.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_68439789BABCFF271828495D57236D60"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.RegSvrEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_68439789BABCFF271828495D57236D60"
                    {
                    "Name" = "8:Microsoft.SqlServer.RegSvrEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.RegSvrEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_684854A22EEF4411850F59466B530D49"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.CollectorEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_684854A22EEF4411850F59466B530D49"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.CollectorEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.CollectorEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_697DD865838F47FE8EE2A438CB99149B"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\ServiceBillingTaxReport.xml"
            "TargetName" = "8:ServiceBillingTaxReport.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_69F7B2743D6B4662ABE755D73BD5278F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.ClientDoc, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_69F7B2743D6B4662ABE755D73BD5278F"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.ClientDoc.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.ClientDoc.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6BDA0A061D0646819C05E3689364CE8C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.ConnectionInfoExtended, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6BDA0A061D0646819C05E3689364CE8C"
                    {
                    "Name" = "8:Microsoft.SqlServer.ConnectionInfoExtended.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.ConnectionInfoExtended.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_71D1457540B24A6297EB19DC578A5500"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\WebCamLib.dll"
            "TargetName" = "8:WebCamLib.dll"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_76CF2E3B5FCB4F8D9C074630112092B3"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\TrialBalance.xml"
            "TargetName" = "8:TrialBalance.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7ACC1127811C896EF1EA2B674633E521"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:TouchlessLib, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_7ACC1127811C896EF1EA2B674633E521"
                    {
                    "Name" = "8:TouchlessLib.DLL"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:TouchlessLib.DLL"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7B1B4987EA4144B2AD6AD864E5ACC061"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Dmf.Adapters, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_7B1B4987EA4144B2AD6AD864E5ACC061"
                    {
                    "Name" = "8:Microsoft.SqlServer.Dmf.Adapters.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Dmf.Adapters.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_7C21ACFFF3094DE79C73D11AB4CF8B8F"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\SalesmanCommissionReport.xml"
            "TargetName" = "8:SalesmanCommissionReport.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8080493E752948ADADF8F2057A5B34EE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SmoExtended, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8080493E752948ADADF8F2057A5B34EE"
                    {
                    "Name" = "8:Microsoft.SqlServer.SmoExtended.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.SmoExtended.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_81A84896E71C1DBE275B03924AA6E9D9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.WmiEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_81A84896E71C1DBE275B03924AA6E9D9"
                    {
                    "Name" = "8:Microsoft.SqlServer.WmiEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.WmiEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_81DA515F1B87EEB1A69CD7396B95B04A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.ConnectionInfo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_81DA515F1B87EEB1A69CD7396B95B04A"
                    {
                    "Name" = "8:Microsoft.SqlServer.ConnectionInfo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.ConnectionInfo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_882356BF36AE4C5184925BFE45ADF546"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.ReportingServices.Interfaces, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_882356BF36AE4C5184925BFE45ADF546"
                    {
                    "Name" = "8:Microsoft.ReportingServices.Interfaces.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.ReportingServices.Interfaces.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_88BE5F9BC6FEBC1ED6F1E453AF0A930F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlWmiManagement, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_88BE5F9BC6FEBC1ED6F1E453AF0A930F"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlWmiManagement.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlWmiManagement.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_8B0DFECCC44449E59CDA4BCCE15CF979"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\ServiceTaxReport.xml"
            "TargetName" = "8:ServiceTaxReport.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8E29E048778F5DAE8BABC8A618D2FD86"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.AnalysisServices, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_8E29E048778F5DAE8BABC8A618D2FD86"
                    {
                    "Name" = "8:Microsoft.AnalysisServices.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.AnalysisServices.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9237AD3867644961BF103193416FDBAF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.Prompting, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9237AD3867644961BF103193416FDBAF"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.Prompting.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.Prompting.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_934D3C8A3D0B4DE2ADACE68D582D6C9A"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\PurchaseDayBook.xml"
            "TargetName" = "8:PurchaseDayBook.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_934DFDC88CC104415AFE14387F9EF299"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlTDiagM, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_934DFDC88CC104415AFE14387F9EF299"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlTDiagM.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlTDiagM.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_93E05772AB298F1A6965DBF92B12AB9B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Diagnostics.STrace, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_93E05772AB298F1A6965DBF92B12AB9B"
                    {
                    "Name" = "8:Microsoft.SqlServer.Diagnostics.STrace.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Diagnostics.STrace.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_94C06A49CA6045DAACC10D100CC16DEA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.CommLayer, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_94C06A49CA6045DAACC10D100CC16DEA"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.CommLayer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.CommLayer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9626213AA0D3410DA87C771F55717B5C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.ConnectionInfo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9626213AA0D3410DA87C771F55717B5C"
                    {
                    "Name" = "8:Microsoft.SqlServer.ConnectionInfo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.ConnectionInfo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_96BA709D0B2A47C69BC0A1A1419524E1"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.Office.Interop.Excel.xml"
            "TargetName" = "8:Microsoft.Office.Interop.Excel.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_97E5E5EDFC3441BDAE86DF6853D37EB6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.Windows.Forms, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_97E5E5EDFC3441BDAE86DF6853D37EB6"
                    {
                    "Name" = "8:CrystalDecisions.Windows.Forms.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.Windows.Forms.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_997B083E644D4DC29875A9D527068B73"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CustomerLedger.xml"
            "TargetName" = "8:CustomerLedger.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9C3C6F5BAA4344A8A03D1F918B90C12A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.Controllers, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9C3C6F5BAA4344A8A03D1F918B90C12A"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.Controllers.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.Controllers.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9E3FA0ADB406437DAFD05F25C7C0F761"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Dmf, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9E3FA0ADB406437DAFD05F25C7C0F761"
                    {
                    "Name" = "8:Microsoft.SqlServer.Dmf.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Dmf.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A435C5538D47D75814645327E81B307C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Dmf, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A435C5538D47D75814645327E81B307C"
                    {
                    "Name" = "8:Microsoft.SqlServer.Dmf.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Dmf.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A549852092D64A39B75968A69A1E3F73"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.SqlParser, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A549852092D64A39B75968A69A1E3F73"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.SqlParser.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.SqlParser.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_A911F0257BDB43CD8EE84DB1C541B1F2"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\ProductBillingTaxReport.xml"
            "TargetName" = "8:ProductBillingTaxReport.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B4FD6F6F8F18442697B01757B8E9AEA3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.ObjectFactory, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_B4FD6F6F8F18442697B01757B8E9AEA3"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.ObjectFactory.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.ObjectFactory.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_B6D94AF8D02448AFB6E61059A4A3D2CA"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Voucher.xml"
            "TargetName" = "8:Voucher.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_B9463F35184A447EBC601AFF338649E6"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\GeneralDayBook.xml"
            "TargetName" = "8:GeneralDayBook.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BE2BB22BDF3940FF8B24E9440FAE2DD0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.RegSvrEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_BE2BB22BDF3940FF8B24E9440FAE2DD0"
                    {
                    "Name" = "8:Microsoft.SqlServer.RegSvrEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.RegSvrEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_BF72CFAFDFB64D7A854AEE9FA6BDA545"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\SupplierLedger.xml"
            "TargetName" = "8:SupplierLedger.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_C0CBE63A65DC41749B72CD508D7F0983"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\TrialBalanceAccounting.xml"
            "TargetName" = "8:TrialBalanceAccounting.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C1E0BDB4670A4B3690E67D24405F5FF5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.CommonObjectModel, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C1E0BDB4670A4B3690E67D24405F5FF5"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.CommonObjectModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.CommonObjectModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C3E630EF5CAB42BD8E1D1C11333336B6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:ShockwaveFlashObjects, Version=1.0.0.0, Culture=neutral, PublicKeyToken=692fbea5521e1304"
                "ScatterAssemblies"
                {
                    "_C3E630EF5CAB42BD8E1D1C11333336B6"
                    {
                    "Name" = "8:ShockwaveFlashObjects.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\ShockwaveFlashObjects.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C523BB6A4EEC714D48AFFD7864827832"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:office, Version=14.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C523BB6A4EEC714D48AFFD7864827832"
                    {
                    "Name" = "8:office.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:office.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_C5CE8785C4C74FF3AD5BD2DA886A2866"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\SQLSettings.dat"
            "TargetName" = "8:SQLSettings.dat"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C7AED02260584C28A7F06E761BA3F670"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.XmlSerialize, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C7AED02260584C28A7F06E761BA3F670"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.XmlSerialize.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.XmlSerialize.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CA1E889B435F198E99D6A977B800EDF5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Vbe.Interop, Version=14.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_CA1E889B435F198E99D6A977B800EDF5"
                    {
                    "Name" = "8:Microsoft.Vbe.Interop.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Vbe.Interop.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CB86E9CEDDB7DBA42B692D24FD942BB1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.SmoMetadataProvider, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_CB86E9CEDDB7DBA42B692D24FD942BB1"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.SmoMetadataProvider.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.SmoMetadataProvider.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_D06404817A0D486F9B78A650EC25064E"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Debtors.xml"
            "TargetName" = "8:Debtors.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D1716580D20F406F884EA3D9BD037A7D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.XEventDbScopedEnum, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D1716580D20F406F884EA3D9BD037A7D"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.XEventDbScopedEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.XEventDbScopedEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D3C31D8012244B40912FF7D8107DE01F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D3C31D8012244B40912FF7D8107DE01F"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.SqlEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D55E489FFB674D2B8C5D765ADC313BDC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlWmiManagement, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_D55E489FFB674D2B8C5D765ADC313BDC"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlWmiManagement.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.SqlWmiManagement.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_D773825E0C0B46A4AC54158C7BABF8D5"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\StockIn.xml"
            "TargetName" = "8:StockIn.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D778102E53E64A9CA00EB2D9E6DF64CD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_D778102E53E64A9CA00EB2D9E6DF64CD"
                    {
                    "Name" = "8:stdole.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\stdole.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_D7F5B31BBFFB42879EC7FBAE2340CD68"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CreditTermsStatementsByCustomer1.xml"
            "TargetName" = "8:CreditTermsStatementsByCustomer1.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DA4E9AC6822E40B8AC92BBF4C1F9A9BA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.XEventDbScoped, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DA4E9AC6822E40B8AC92BBF4C1F9A9BA"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.XEventDbScoped.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.XEventDbScoped.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DB6298CA6DFB497499E6325C8FDF7488"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.PolicyEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DB6298CA6DFB497499E6325C8FDF7488"
                    {
                    "Name" = "8:Microsoft.SqlServer.PolicyEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.PolicyEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DBFC1AF59F0D4513BBF79314E5E76499"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.XEvent, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DBFC1AF59F0D4513BBF79314E5E76499"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.XEvent.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Management.XEvent.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_DCA61F7BD1EA47443F4BE8888BAEA92A"
            {
            "SourcePath" = "8:Flash32_25_0_0_171.ocx"
            "TargetName" = "8:Flash32_25_0_0_171.ocx"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_DDFBA09E7999406BAF02DE4AAEBD336F"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CreditTermsStatementsByCustomer.xml"
            "TargetName" = "8:CreditTermsStatementsByCustomer.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DE7524DA993AB5C6E729AF8D26078E71"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.Sdk.Sfc, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DE7524DA993AB5C6E729AF8D26078E71"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.Sdk.Sfc.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.Sdk.Sfc.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E00F1C275B9D2D602C6CA9FBC2C9A7F5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Management.XEvent, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E00F1C275B9D2D602C6CA9FBC2C9A7F5"
                    {
                    "Name" = "8:Microsoft.SqlServer.Management.XEvent.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.Management.XEvent.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_E31767B763F34B61A36A0B783AF79E38"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\GeneralLedger.xml"
            "TargetName" = "8:GeneralLedger.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E4CF529E388B440CB7EEE650370228C9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportSource, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E4CF529E388B440CB7EEE650370228C9"
                    {
                    "Name" = "8:CrystalDecisions.ReportSource.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportSource.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E52B23B60E6A4BF589EDE77A4991E04A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.Smo, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E52B23B60E6A4BF589EDE77A4991E04A"
                    {
                    "Name" = "8:Microsoft.SqlServer.Smo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\Microsoft.SqlServer.Smo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_E8E300C4F3824363849CE1DA63AA851F"
            {
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CreditTermsStatements.xml"
            "TargetName" = "8:CreditTermsStatements.xml"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EAD867ED1F2B4039B965812C509147AF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.ReportDefModel, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_EAD867ED1F2B4039B965812C509147AF"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.ReportDefModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.ReportDefModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EE59B4FD326A4E529DDF8B7A22ADB4FB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:TouchlessLib, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86"
                "ScatterAssemblies"
                {
                    "_EE59B4FD326A4E529DDF8B7A22ADB4FB"
                    {
                    "Name" = "8:TouchlessLib.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\TouchlessLib.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F41DBE7D07BA4A7F9274CF2D8371032D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:FlashControlV71, Version=1.0.3187.32366, Culture=neutral, PublicKeyToken=692fbea5521e1304"
                "ScatterAssemblies"
                {
                    "_F41DBE7D07BA4A7F9274CF2D8371032D"
                    {
                    "Name" = "8:FlashControlV71.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\FlashControlV71.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FC2B6339F1E6484C914F04FE30B00D10"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CrystalDecisions.ReportAppServer.CommonControls, Version=13.0.2000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FC2B6339F1E6484C914F04FE30B00D10"
                    {
                    "Name" = "8:CrystalDecisions.ReportAppServer.CommonControls.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:..\\..\\..\\Inventory Software v4.0.1.0 - Arabic\\Sales and Inventory System\\bin\\Debug\\CrystalDecisions.ReportAppServer.CommonControls.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FC8576C493CA8ECBDEAE984B695EF06D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.ServiceBrokerEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FC8576C493CA8ECBDEAE984B695EF06D"
                    {
                    "Name" = "8:Microsoft.SqlServer.ServiceBrokerEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.ServiceBrokerEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FE15720A0F094DAF559F7EE25DF97070"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:Microsoft.SqlServer.SqlEnum, Version=10.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FE15720A0F094DAF559F7EE25DF97070"
                    {
                    "Name" = "8:Microsoft.SqlServer.SqlEnum.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.SqlServer.SqlEnum.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
        }
        "FileType"
        {
        }
        "Folder"
        {
            "{1525181F-901A-416C-8A58-119130FE478E}:_34412C19A2FD4DC18BBE359674A04A05"
            {
            "Name" = "8:#1919"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:ProgramMenuFolder"
                "Folders"
                {
                }
            }
            "{3C67513D-01DD-4637-8A68-80971EB9504F}:_C03184C1FC2F4A12B490111CA26EF604"
            {
            "DefaultLocation" = "8:[ProgramFilesFolder][Manufacturer]\\[ProductName]"
            "Name" = "8:#1925"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:TARGETDIR"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_C90C06E8A81B4BA6B3D2A4BCD3608DB3"
            {
            "Name" = "8:#1916"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:DesktopFolder"
                "Folders"
                {
                }
            }
        }
        "LaunchCondition"
        {
        }
        "Locator"
        {
        }
        "MsiBootstrapper"
        {
        "LangId" = "3:1033"
        "RequiresElevation" = "11:FALSE"
        }
        "Product"
        {
        "Name" = "8:Microsoft Visual Studio"
        "ProductName" = "8:Setup1"
        "ProductCode" = "8:{98250EA5-47C9-41F7-9C6C-15FF9B030EDF}"
        "PackageCode" = "8:{9A74F57E-3D62-4B81-9E37-2711EE5BF977}"
        "UpgradeCode" = "8:{10C45B10-0870-4822-BD5E-9D906C390BBD}"
        "AspNetVersion" = "8:4.0.30319.0"
        "RestartWWWService" = "11:FALSE"
        "RemovePreviousVersions" = "11:FALSE"
        "DetectNewerInstalledVersion" = "11:TRUE"
        "InstallAllUsers" = "11:FALSE"
        "ProductVersion" = "8:1.0.0"
        "Manufacturer" = "8:Default Company Name"
        "ARPHELPTELEPHONE" = "8:"
        "ARPHELPLINK" = "8:"
        "Title" = "8:Setup1"
        "Subject" = "8:"
        "ARPCONTACT" = "8:Default Company Name"
        "Keywords" = "8:"
        "ARPCOMMENTS" = "8:"
        "ARPURLINFOABOUT" = "8:"
        "ARPPRODUCTICON" = "8:"
        "ARPIconIndex" = "3:0"
        "SearchPath" = "8:"
        "UseSystemSearchPath" = "11:TRUE"
        "TargetPlatform" = "3:0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
        }
        "Registry"
        {
            "HKLM"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_A0F785F76B00462981756238BB95B0E3"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_0D8B1E8819B24E148B6F0C409D706DF9"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCU"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_C29022ECBD4D41D98CA5C57FF38AC21D"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_C0A14757933645E1AF655F79E42CFE60"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCR"
            {
                "Keys"
                {
                }
            }
            "HKU"
            {
                "Keys"
                {
                }
            }
            "HKPU"
            {
                "Keys"
                {
                }
            }
        }
        "Sequences"
        {
        }
        "Shortcut"
        {
            "{970C0BB2-C7D0-45D7-ABFA-7EC378858BC0}:_86E610560CEC49A79D19A3173A7F3569"
            {
            "Name" = "8:NovaSoft.exe"
            "Arguments" = "8:"
            "Description" = "8:"
            "ShowCmd" = "3:1"
            "IconIndex" = "3:0"
            "Transitive" = "11:FALSE"
            "Target" = "8:_12077EFA8FF84EE2A321BA83C93D07E7"
            "Folder" = "8:_C90C06E8A81B4BA6B3D2A4BCD3608DB3"
            "WorkingFolder" = "8:_C03184C1FC2F4A12B490111CA26EF604"
            "Icon" = "8:_353D608B1AC3445ABB3E1294809ADE93"
            "Feature" = "8:"
            }
        }
        "UserInterface"
        {
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_10E129472D0644EC902F202E9BC42553"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:2"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_0737751A5CD7465194B46C828FB10C09"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_2EF0E284699443619057051A0A67B6A6"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdUserInterface.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_3421E964AEEB4B1DB908963FBE25ADC6"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:2"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_6CD1E6157EFA4B098AB0F8A0A1F56533"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_85FBC03801EB48C69538B11FF26331D7"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_9B1C0BE546684A09955480FEDF20F195"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_5795B0E77FA64611AC19CC6930096A1C"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdBasicDialogs.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_820ED890454841B285145B58F61268C4"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:1"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_2D9A84A0C33E43B28FC2AA6F54D63BCC"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_64D6A7953A5C4730B049CC8CD9CE8426"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "InstallAllUsersVisible"
                            {
                            "Name" = "8:InstallAllUsersVisible"
                            "DisplayName" = "8:#1059"
                            "Description" = "8:#1159"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_A4C3A10799754E5A9FA6E5A8925A1D85"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_C63BF2F5A6924F2B919B3336F1833FCA"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:2"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_704E56D28A52428BAFB4CF167A7162AC"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_DB9F61CBDCE54394801C0B0A22103FA6"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:1"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_A53AF465100842708C44BAEF79CF0957"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "UpdateText"
                            {
                            "Name" = "8:UpdateText"
                            "DisplayName" = "8:#1058"
                            "Description" = "8:#1158"
                            "Type" = "3:15"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1258"
                            "DefaultValue" = "8:#1258"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_E9068851675B4459AF0460110D3FBC17"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:1"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_79E5BD0CBF094C36ADF0A2C9DEA50D84"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
        }
        "MergeModule"
        {
        }
        "ProjectOutput"
        {
        }
    }
}
