Warning	1	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	2	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	3	The primary reference "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	4	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	5	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	6	The primary reference "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	7	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	8	The primary reference "Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it has an indirect dependency on the assembly "Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" which was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	9	The primary reference "Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" could not be resolved because it was built against the ".NETFramework,Version=v4.6" framework. This is a higher version than the currently targeted framework ".NETFramework,Version=v4.0".	Sales and Inventory System
Warning	10	Could not resolve this reference. Could not locate the assembly "TouchlessLib". Check to make sure the assembly exists on disk. If this reference is required by your code, you may get compilation errors.	Sales and Inventory System
Warning	11	Cannot get the file path for type library "237f4bec-8ae5-41e1-ae84-b194e4670597" version 13.0. Library not registered. (Exception from HRESULT: 0x8002801D (TYPE_E_LIBNOTREGISTERED))	Sales and Inventory System
Warning	12	The referenced component 'TouchlessLib' could not be found. 	Sales and Inventory System
Warning	13	The referenced component 'PAGEOBJECTMODELLib' could not be found. 	Sales and Inventory System
Warning	14	Namespace or type specified in the Imports 'Microsoft.Reporting.WinForms' doesn't contain any public member or cannot be found. Make sure the namespace or the type is defined and contains at least one public member. Make sure the imported element name doesn't use any aliases.	D:\b Sales and Inventory System\ReportManager.vb	2	9	Sales and Inventory System
Error	15	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	62	43	Sales and Inventory System
Error	16	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	63	32	Sales and Inventory System
Error	17	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	64	32	Sales and Inventory System
Error	18	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	65	32	Sales and Inventory System
Error	19	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	82	43	Sales and Inventory System
Error	20	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	83	32	Sales and Inventory System
Error	21	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	84	32	Sales and Inventory System
Error	22	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	85	32	Sales and Inventory System
Error	23	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	102	43	Sales and Inventory System
Error	24	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	103	32	Sales and Inventory System
Error	25	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	104	32	Sales and Inventory System
Error	26	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	105	32	Sales and Inventory System
Error	27	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	122	43	Sales and Inventory System
Error	28	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	123	32	Sales and Inventory System
Error	29	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	140	43	Sales and Inventory System
Error	30	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	141	32	Sales and Inventory System
Error	31	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	142	32	Sales and Inventory System
Error	32	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	143	32	Sales and Inventory System
Error	33	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	160	43	Sales and Inventory System
Error	34	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	161	32	Sales and Inventory System
Error	35	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	162	32	Sales and Inventory System
Error	36	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	163	32	Sales and Inventory System
Error	37	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	180	43	Sales and Inventory System
Error	38	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	181	32	Sales and Inventory System
Error	39	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	182	32	Sales and Inventory System
Error	40	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	183	32	Sales and Inventory System
Error	41	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	184	32	Sales and Inventory System
Error	42	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	201	43	Sales and Inventory System
Error	43	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	202	32	Sales and Inventory System
Error	44	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	203	32	Sales and Inventory System
Error	45	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	204	32	Sales and Inventory System
Error	46	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	205	32	Sales and Inventory System
Error	47	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	222	43	Sales and Inventory System
Error	48	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	223	32	Sales and Inventory System
Error	49	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	224	32	Sales and Inventory System
Error	50	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	225	32	Sales and Inventory System
Error	51	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	226	32	Sales and Inventory System
Error	52	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	243	43	Sales and Inventory System
Error	53	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	244	32	Sales and Inventory System
Error	54	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	261	43	Sales and Inventory System
Error	55	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	262	32	Sales and Inventory System
Error	56	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	279	43	Sales and Inventory System
Error	57	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	280	32	Sales and Inventory System
Error	58	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	281	32	Sales and Inventory System
Error	59	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	282	32	Sales and Inventory System
Error	60	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	299	43	Sales and Inventory System
Error	61	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	300	32	Sales and Inventory System
Error	62	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	301	32	Sales and Inventory System
Error	63	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	302	32	Sales and Inventory System
Error	64	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	319	43	Sales and Inventory System
Error	65	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	320	32	Sales and Inventory System
Error	66	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	321	32	Sales and Inventory System
Error	67	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	338	43	Sales and Inventory System
Error	68	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	339	32	Sales and Inventory System
Error	69	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	340	32	Sales and Inventory System
Error	70	Type 'ReportParameter' is not defined.	D:\b Sales and Inventory System\ReportManager.vb	341	32	Sales and Inventory System
Error	71	'Picsart_23_03_19_11_27_15_052' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmAbout.Designer.vb	73	35	Sales and Inventory System
Error	72	'Activate' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmActivation.Designer.vb	159	28	Sales and Inventory System
Error	73	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmChangePassword.Designer.vb	186	30	Sales and Inventory System
Error	74	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmChangePassword.Designer.vb	203	32	Sales and Inventory System
Error	75	'Company1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmCompany.Designer.vb	140	32	Sales and Inventory System
Error	76	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmCreditTermsStatementsReport.vb	86	13	Sales and Inventory System
Error	77	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmCreditTermsStatementsReport.vb	156	13	Sales and Inventory System
Error	78	'photo' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmCustomer.Designer.vb	247	28	Sales and Inventory System
Error	79	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmCustomer.vb	26	25	Sales and Inventory System
Error	80	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmCustomer.vb	380	25	Sales and Inventory System
Error	81	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmCustomerLedger.vb	90	13	Sales and Inventory System
Error	82	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmDebtorsReport.vb	35	13	Sales and Inventory System
Error	83	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmDebtorsReport.vb	54	13	Sales and Inventory System
Error	84	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmDebtorsReport.vb	68	13	Sales and Inventory System
Error	85	'Picsart_23_03_19_11_27_15_0522' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	152	35	Sales and Inventory System
Error	86	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	203	32	Sales and Inventory System
Error	87	'panelControl1_ContentImage' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogin.Designer.vb	241	30	Sales and Inventory System
Error	88	'Reset2_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	107	29	Sales and Inventory System
Error	89	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	217	29	Sales and Inventory System
Error	90	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmLogs.Designer.vb	240	37	Sales and Inventory System
Error	91	'Billing_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	173	45	Sales and Inventory System
Error	92	'basket_full_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmMainMenu.Designer.vb	220	45	Sales and Inventory System
Error	93	'keyboard_icon__1_' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1604	32	Sales and Inventory System
Error	94	'User_Interface_Restore_Window_icon__1_' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1622	32	Sales and Inventory System
Error	95	'Programming_Minimize_Window_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1640	32	Sales and Inventory System
Error	96	'Button_Delete_icon11' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPOS.Designer.vb	1657	29	Sales and Inventory System
Error	97	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment.Designer.vb	167	29	Sales and Inventory System
Error	98	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment_2.Designer.vb	283	29	Sales and Inventory System
Error	99	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPayment_3.Designer.vb	277	29	Sales and Inventory System
Error	100	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmProfitAndLossReport.vb	48	13	Sales and Inventory System
Error	101	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmPurchaseReturn.Designer.vb	675	29	Sales and Inventory System
Error	102	'Button_Delete_icon1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmRecoveryPassword.Designer.vb	111	30	Sales and Inventory System
Error	103	'ModernXP_09_Keyboard_icon__1_1' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmRecoveryPassword.Designer.vb	129	32	Sales and Inventory System
Error	104	Type 'Microsoft.Reporting.WinForms.ReportViewer' is not defined.	D:\b Sales and Inventory System\frmReportViewer.Designer.vb	26	32	Sales and Inventory System
Error	105	Type 'Microsoft.Reporting.WinForms.ReportViewer' is not defined.	D:\b Sales and Inventory System\frmReportViewer.Designer.vb	121	40	Sales and Inventory System
Error	106	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSalesReport.vb	49	13	Sales and Inventory System
Error	107	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSalesReport.vb	79	13	Sales and Inventory System
Error	108	'product_sales_report_icon' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSalesReturn.Designer.vb	1048	29	Sales and Inventory System
Error	109	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSalesReturn.vb	111	13	Sales and Inventory System
Error	110	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmSalesman.vb	23	25	Sales and Inventory System
Error	111	'photo' is not a member of 'Resources'.	D:\b Sales and Inventory System\frmSalesman.vb	304	25	Sales and Inventory System
Error	112	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSalesmanCommissionReport.vb	55	13	Sales and Inventory System
Error	113	'Close_32x32' is not a member of 'Sales_and_Inventory_System.My.Resources.Resources'.	D:\b Sales and Inventory System\frmSupplier.Designer.vb	121	29	Sales and Inventory System
Error	114	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmSupplierLedger.vb	82	13	Sales and Inventory System
Error	115	'ReportManager' is not declared. It may be inaccessible due to its protection level.	D:\b Sales and Inventory System\frmTrialBalance.vb	57	13	Sales and Inventory System
Error	116	Maximum number of errors has been exceeded.	Sales and Inventory System
Warning	117	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditors.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	118	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTerms.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	119	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptCreditTermsStatements.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	120	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	121	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales1.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	122	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSales2.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	123	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptSalesmanLedger.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	124	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptService.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	125	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'rptStockOut.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	126	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTpurchases.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
Warning	127	A custom tool 'CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator' is associated with file 'SubRPTservice.rpt', but the output of the custom tool was not found in the project.  You may try re-running the custom tool by right-clicking on the file in the Solution Explorer and choosing Run Custom Tool.	Sales and Inventory System
