﻿Imports System.Data.SqlClient


Public Class frmStockInAndOutReport

    Private Sub btnClose_Click_1(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub Timer1_Tick(sender As System.Object, e As System.EventArgs) Handles Timer1.Tick
        Cursor = Cursors.Default
        Timer1.Enabled = False
    End Sub

    Private Sub btnGetData_Click(sender As System.Object, e As System.EventArgs) Handles btnStockIn.Click
        Try
            ' استخدام النظام الجديد للتقارير - تقرير المخزون المتوفر
            Sales_and_Inventory_System.ReportManager.ShowStockInReport(DateTime.Now.AddDays(-30), DateTime.Now)
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub Button1_Click(sender As System.Object, e As System.EventArgs) Handles btnStockOut.Click
        Try
            ' استخدام النظام الجديد للتقارير - تقرير المخزون المنتهي
            Sales_and_Inventory_System.ReportManager.ShowStockOutReport(DateTime.Now.AddDays(-30), DateTime.Now)
        Catch ex As Exception
            MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub frmStockInAndOutReport_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load

    End Sub
End Class
