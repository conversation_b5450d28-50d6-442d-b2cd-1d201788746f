<?xml version="1.0" encoding="utf-8"?><CrystalReportEngine-configuration>
    <timeout>10</timeout>
    
    <ExternalFunctionLibraryClassNames> 
		<classname> </classname>
		<classname> </classname>
    </ExternalFunctionLibraryClassNames>
    

<CommonBinDir>C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\bin</CommonBinDir>

<Javaserver-configuration>
<DataDriverCommon>
	<JavaDir32>C:\Program Files (x86)\Java\jre7\bin</JavaDir32>
	<Classpath>C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\crlovmanifest.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\CRLOVExternal.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\CRDBJDBCServer.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\CRDBXMLServer.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\CRDBJavaBeansServer.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\external\log4j.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\CRDBSForceServer.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\external\CRDBSForceExternal.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\CRDBWicServer.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\external\CRDBWicExternal.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\external\CRDBXMLExternal.jar;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\CRSDKCommon.jar,C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\rascore.jar;${CLASSPATH}</Classpath>
	<IORFileLocation>${TEMP}</IORFileLocation>
	<JavaServerTimeout>1800</JavaServerTimeout>
	<JavaServerStartupTimeout>30</JavaServerStartupTimeout>
	<JVMMaxHeap>64000000</JVMMaxHeap>
	<JVMMinHeap>32000000</JVMMinHeap>
	<NumberOfThreads>100</NumberOfThreads>
	<UseProxy>FALSE</UseProxy>
	<ProxyAddress></ProxyAddress>
	<ProxyPort></ProxyPort>
	<ProxyUsername></ProxyUsername>
	<ProxyPassword></ProxyPassword>
</DataDriverCommon>
<JDBC>
	<CacheRowSetSize>100</CacheRowSetSize>
	<JDBCURL></JDBCURL>
	<JDBCClassName></JDBCClassName>
	<JDBCUserName></JDBCUserName>
	<JNDIURL></JNDIURL>
	<JNDIConnectionFactory></JNDIConnectionFactory>
	<JNDIInitContext>/</JNDIInitContext>
	<JNDIUserName>weblogic</JNDIUserName>
	<GenericJDBCDriver>
		<Default>
			<ServerType>UNKNOWN</ServerType>
			<QuoteIdentifierOnOff>ON</QuoteIdentifierOnOff>
			<StoredProcType>Standard</StoredProcType>
			<LogonStyle>Standard</LogonStyle>
		</Default>
		<Sybase>
			<ServerType>SYBASE</ServerType>
			<QuoteIdentifierOnOff>OFF</QuoteIdentifierOnOff>
			<DriverClassName>com.sybase.jdbc2.jdbc.SybDriver</DriverClassName>
			<StoredProcType>Standard</StoredProcType>
			<LogonStyle>MySQL</LogonStyle>
		</Sybase>
	</GenericJDBCDriver>
</JDBC>
<XML>
	<CacheRowSetSize>100</CacheRowSetSize>
	<PreReadNBytes>4096</PreReadNBytes>
	<MaxCacheXMLSize>153600</MaxCacheXMLSize>
	<XMLLocalURL></XMLLocalURL>
	<SchemaLocalURL></SchemaLocalURL>
	<XMLHttpURL></XMLHttpURL>
	<SchemaHttpURL></SchemaHttpURL>
	<RepositoryPath>C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\lib\external</RepositoryPath>
	<ExternalNamespace>C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\xsd\wsdl.xsd;C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\java\xsd\wsdl_encoding.xsd</ExternalNamespace>
	<SocketTimeout>60000</SocketTimeout>
</XML>
<JavaBeans>
    <CacheRowSetSize>100</CacheRowSetSize>
	<JavaBeansClassPath></JavaBeansClassPath>
	<CallBackFunction>CrystalReportsLogoff</CallBackFunction>
</JavaBeans>
<SForce>
         <CacheRowSetSize>100</CacheRowSetSize>
         <QueryBatchSize>2000</QueryBatchSize>
         <SocketTimeout>600000</SocketTimeout>
         <BatchLookupCacheJoinSize>200</BatchLookupCacheJoinSize>
</SForce>
</Javaserver-configuration>

</CrystalReportEngine-configuration>
