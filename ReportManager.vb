Imports System.Data.SqlClient
Imports Microsoft.Reporting.WinForms

Public Class ReportManager
    
    ' دالة لعرض تقرير الفاتورة
    Public Shared Sub ShowInvoiceReport(invoiceNo As String, customerType As String)
        Dim frmReport As New frmReportViewer()
        frmReport.ShowInvoiceReport(invoiceNo, customerType)
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير المبيعات
    Public Shared Sub ShowSalesReport(dateFrom As DateTime, dateTo As DateTime)
        Dim frmReport As New frmReportViewer()
        frmReport.ShowSalesReport(dateFrom, dateTo)
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير المشتريات
    Public Shared Sub ShowPurchaseReport(dateFrom As DateTime, dateTo As DateTime)
        Dim frmReport As New frmReportViewer()
        frmReport.ShowPurchaseReport(dateFrom, dateTo)
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير المخزون
    Public Shared Sub ShowStockReport()
        Dim frmReport As New frmReportViewer()
        frmReport.ShowStockReport()
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير العملاء
    Public Shared Sub ShowCustomerReport()
        Dim frmReport As New frmReportViewer()
        frmReport.ShowCustomerReport()
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير الموردين
    Public Shared Sub ShowSupplierReport()
        Dim frmReport As New frmReportViewer()
        frmReport.ShowSupplierReport()
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير الأرباح والخسائر
    Public Shared Sub ShowProfitLossReport(dateFrom As DateTime, dateTo As DateTime)
        Dim frmReport As New frmReportViewer()
        frmReport.ShowProfitLossReport(dateFrom, dateTo)
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير دفتر الأستاذ العام
    Public Shared Sub ShowGeneralLedgerReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadGeneralLedgerData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\GeneralLedgerReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر الأستاذ: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير دفتر اليومية العام
    Public Shared Sub ShowGeneralDayBookReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadGeneralDayBookData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\GeneralDayBookReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر اليومية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير المدينين
    Public Shared Sub ShowDebtorsReport()
        Try
            Dim ds As New DataSet()
            LoadDebtorsData(ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\DebtorsReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير المدينين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير الدائنين
    Public Shared Sub ShowCreditorsReport()
        Try
            Dim ds As New DataSet()
            LoadCreditorsData(ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\CreditorsReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير الدائنين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير الضرائب
    Public Shared Sub ShowTaxReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadTaxData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\TaxReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير الضرائب: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير عمولات المندوبين
    Public Shared Sub ShowSalesmanCommissionReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadSalesmanCommissionData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\SalesmanCommissionReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير عمولات المندوبين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير دفتر أستاذ العميل
    Public Shared Sub ShowCustomerLedgerReport(customerID As String, dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadCustomerLedgerData(customerID, dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("CustomerID", customerID))
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\CustomerLedgerReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر أستاذ العميل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير دفتر أستاذ المورد
    Public Shared Sub ShowSupplierLedgerReport(supplierID As String, dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadSupplierLedgerData(supplierID, dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("SupplierID", supplierID))
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\SupplierLedgerReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر أستاذ المورد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دوال تحميل البيانات للتقارير الإضافية
    Private Shared Sub LoadGeneralLedgerData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit, PartyID FROM LedgerBook WHERE Date BETWEEN @dateFrom AND @dateTo ORDER BY Date", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("GeneralLedger")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadGeneralDayBookData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit FROM LedgerBook WHERE Date BETWEEN @dateFrom AND @dateTo ORDER BY Date, LedgerNo", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("GeneralDayBook")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadDebtorsData(ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT * FROM Debitors", con)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("Debtors")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadCreditorsData(ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT * FROM Creditors", con)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("Creditors")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadTaxData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            ' تقرير ضرائب المبيعات
            Dim cmdSalesTax As New SqlCommand("SELECT InvoiceInfo.InvoiceNo, InvoiceInfo.InvoiceDate, Customer.Name AS CustomerName, Invoice_Product.VATPer, Invoice_Product.VAT FROM InvoiceInfo INNER JOIN Customer ON InvoiceInfo.CustomerID = Customer.ID INNER JOIN Invoice_Product ON InvoiceInfo.Inv_ID = Invoice_Product.InvoiceID WHERE InvoiceInfo.InvoiceDate BETWEEN @dateFrom AND @dateTo AND Invoice_Product.VAT > 0 ORDER BY InvoiceInfo.InvoiceDate", con)
            cmdSalesTax.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdSalesTax.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adpSalesTax As New SqlDataAdapter(cmdSalesTax)
            Dim dtSalesTax As New DataTable("SalesTax")
            adpSalesTax.Fill(dtSalesTax)
            ds.Tables.Add(dtSalesTax)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadSalesmanCommissionData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Salesman.SalesmanName, InvoiceInfo.InvoiceNo, InvoiceInfo.InvoiceDate, InvoiceInfo.GrandTotal, (InvoiceInfo.GrandTotal * Salesman.Commission / 100) AS CommissionAmount FROM InvoiceInfo INNER JOIN Salesman ON InvoiceInfo.SalesmanID = Salesman.SalesmanID WHERE InvoiceInfo.InvoiceDate BETWEEN @dateFrom AND @dateTo ORDER BY Salesman.SalesmanName, InvoiceInfo.InvoiceDate", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("SalesmanCommission")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadCustomerLedgerData(customerID As String, dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit FROM LedgerBook WHERE PartyID = @customerID AND Date BETWEEN @dateFrom AND @dateTo ORDER BY Date", con)
            cmd.Parameters.AddWithValue("@customerID", customerID)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("CustomerLedger")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            ' تحميل بيانات العميل
            Dim cmdCustomer As New SqlCommand("SELECT * FROM Customer WHERE CustomerID = @customerID", con)
            cmdCustomer.Parameters.AddWithValue("@customerID", customerID)
            Dim adpCustomer As New SqlDataAdapter(cmdCustomer)
            Dim dtCustomer As New DataTable("Customer")
            adpCustomer.Fill(dtCustomer)
            ds.Tables.Add(dtCustomer)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadSupplierLedgerData(supplierID As String, dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit FROM SupplierLedgerBook WHERE PartyID = @supplierID AND Date BETWEEN @dateFrom AND @dateTo ORDER BY Date", con)
            cmd.Parameters.AddWithValue("@supplierID", supplierID)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("SupplierLedger")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            ' تحميل بيانات المورد
            Dim cmdSupplier As New SqlCommand("SELECT * FROM Supplier WHERE SupplierID = @supplierID", con)
            cmdSupplier.Parameters.AddWithValue("@supplierID", supplierID)
            Dim adpSupplier As New SqlDataAdapter(cmdSupplier)
            Dim dtSupplier As New DataTable("Supplier")
            adpSupplier.Fill(dtSupplier)
            ds.Tables.Add(dtSupplier)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadCompanyDataStatic(ds As DataSet)
        Try
            If con.State = ConnectionState.Closed Then
                con = New SqlConnection(cs)
                con.Open()
            End If

            Dim cmd As New SqlCommand("SELECT * FROM Company", con)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("Company")
            adp.Fill(dt)

            If Not ds.Tables.Contains("Company") Then
                ds.Tables.Add(dt)
            End If

        Catch ex As Exception
            Throw ex
        End Try
    End Sub
End Class
