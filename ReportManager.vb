Imports System.Data.SqlClient
Imports Microsoft.Reporting.WinForms

Public Class ReportManager
    
    ' دالة لعرض تقرير الفاتورة
    Public Shared Sub ShowInvoiceReport(invoiceNo As String, customerType As String)
        Dim frmReport As New frmReportViewer()
        frmReport.ShowInvoiceReport(invoiceNo, customerType)
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير المبيعات
    Public Shared Sub ShowSalesReport(dateFrom As DateTime, dateTo As DateTime)
        Dim frmReport As New frmReportViewer()
        frmReport.ShowSalesReport(dateFrom, dateTo)
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير المشتريات
    Public Shared Sub ShowPurchaseReport(dateFrom As DateTime, dateTo As DateTime)
        Dim frmReport As New frmReportViewer()
        frmReport.ShowPurchaseReport(dateFrom, dateTo)
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير المخزون
    Public Shared Sub ShowStockReport()
        Dim frmReport As New frmReportViewer()
        frmReport.ShowStockReport()
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير العملاء
    Public Shared Sub ShowCustomerReport()
        Dim frmReport As New frmReportViewer()
        frmReport.ShowCustomerReport()
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير الموردين
    Public Shared Sub ShowSupplierReport()
        Dim frmReport As New frmReportViewer()
        frmReport.ShowSupplierReport()
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير الأرباح والخسائر
    Public Shared Sub ShowProfitLossReport(dateFrom As DateTime, dateTo As DateTime)
        Dim frmReport As New frmReportViewer()
        frmReport.ShowProfitLossReport(dateFrom, dateTo)
        frmReport.ShowDialog()
    End Sub
    
    ' دالة لعرض تقرير دفتر الأستاذ العام
    Public Shared Sub ShowGeneralLedgerReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadGeneralLedgerData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\GeneralLedgerReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر الأستاذ: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير دفتر اليومية العام
    Public Shared Sub ShowGeneralDayBookReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadGeneralDayBookData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\GeneralDayBookReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر اليومية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير المدينين
    Public Shared Sub ShowDebtorsReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadDebtorsData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\DebtorsReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير المدينين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير الدائنين
    Public Shared Sub ShowCreditorsReport()
        Try
            Dim ds As New DataSet()
            LoadCreditorsData(ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\CreditorsReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير الدائنين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير الضرائب
    Public Shared Sub ShowTaxReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadTaxData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\TaxReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير الضرائب: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير عمولات المندوبين
    Public Shared Sub ShowSalesmanCommissionReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadSalesmanCommissionData(dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\SalesmanCommissionReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير عمولات المندوبين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير دفتر أستاذ العميل
    Public Shared Sub ShowCustomerLedgerReport(customerID As String, dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadCustomerLedgerData(customerID, dateFrom, dateTo, ds)
            
            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("CustomerID", customerID))
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))
            
            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\CustomerLedgerReport.rdlc", ds, parameters)
            frmReport.ShowDialog()
            
        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر أستاذ العميل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' دالة لعرض تقرير دفتر أستاذ المورد
    Public Shared Sub ShowSupplierLedgerReport(supplierID As String, dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadSupplierLedgerData(supplierID, dateFrom, dateTo, ds)

            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("SupplierID", supplierID))
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))

            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\SupplierLedgerReport.rdlc", ds, parameters)
            frmReport.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر أستاذ المورد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير المشتريات حسب المورد
    Public Shared Sub ShowSupplierPurchaseReport(supplierName As String, dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadSupplierPurchaseData(supplierName, dateFrom, dateTo, ds)

            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("SupplierName", supplierName))
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))

            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\SupplierPurchaseReport.rdlc", ds, parameters)
            frmReport.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير مشتريات المورد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير المخزون المتوفر
    Public Shared Sub ShowStockInReport()
        Try
            Dim ds As New DataSet()
            LoadStockInData(ds)

            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))

            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\StockInReport.rdlc", ds, parameters)
            frmReport.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير المخزون المتوفر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير المخزون المنتهي
    Public Shared Sub ShowStockOutReport()
        Try
            Dim ds As New DataSet()
            LoadStockOutData(ds)

            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))

            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\StockOutReport.rdlc", ds, parameters)
            frmReport.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير المخزون المنتهي: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير ضريبة المبيعات
    Public Shared Sub ShowSalesTaxReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadSalesTaxData(dateFrom, dateTo, ds)

            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))

            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\SalesTaxReport.rdlc", ds, parameters)
            frmReport.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير ضريبة المبيعات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير ضريبة الخدمات
    Public Shared Sub ShowServiceTaxReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadServiceTaxData(dateFrom, dateTo, ds)

            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))

            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\ServiceTaxReport.rdlc", ds, parameters)
            frmReport.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير ضريبة الخدمات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير دفتر اليومية
    Public Shared Sub ShowGeneralDayBookReport(reportDate As DateTime)
        Try
            Dim ds As New DataSet()
            LoadGeneralDayBookData(reportDate, ds)

            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("ReportDate", reportDate.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))

            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\GeneralDayBookReport.rdlc", ds, parameters)
            frmReport.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير دفتر اليومية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دالة لعرض تقرير الأرباح والخسائر
    Public Shared Sub ShowProfitAndLossReport(dateFrom As DateTime, dateTo As DateTime)
        Try
            Dim ds As New DataSet()
            LoadProfitAndLossData(dateFrom, dateTo, ds)

            Dim parameters As New List(Of ReportParameter)()
            parameters.Add(New ReportParameter("DateFrom", dateFrom.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("DateTo", dateTo.ToString("dd/MM/yyyy")))
            parameters.Add(New ReportParameter("PrintDate", DateTime.Now.ToString("dd/MM/yyyy")))

            Dim frmReport As New frmReportViewer()
            frmReport.ShowCustomReport(Application.StartupPath & "\Reports\ProfitAndLossReport.rdlc", ds, parameters)
            frmReport.ShowDialog()

        Catch ex As Exception
            MessageBox.Show("خطأ في عرض تقرير الأرباح والخسائر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' دوال تحميل البيانات للتقارير الإضافية
    Private Shared Sub LoadGeneralLedgerData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit, PartyID FROM LedgerBook WHERE Date BETWEEN @dateFrom AND @dateTo ORDER BY Date", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("GeneralLedger")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadGeneralDayBookData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit FROM LedgerBook WHERE Date BETWEEN @dateFrom AND @dateTo ORDER BY Date, LedgerNo", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("GeneralDayBook")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadDebtorsData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT * FROM Debitors WHERE Date BETWEEN @dateFrom AND @dateTo", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("Debtors")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadCreditorsData(ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT * FROM Creditors", con)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("Creditors")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadTaxData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            ' تقرير ضرائب المبيعات
            Dim cmdSalesTax As New SqlCommand("SELECT InvoiceInfo.InvoiceNo, InvoiceInfo.InvoiceDate, Customer.Name AS CustomerName, Invoice_Product.VATPer, Invoice_Product.VAT FROM InvoiceInfo INNER JOIN Customer ON InvoiceInfo.CustomerID = Customer.ID INNER JOIN Invoice_Product ON InvoiceInfo.Inv_ID = Invoice_Product.InvoiceID WHERE InvoiceInfo.InvoiceDate BETWEEN @dateFrom AND @dateTo AND Invoice_Product.VAT > 0 ORDER BY InvoiceInfo.InvoiceDate", con)
            cmdSalesTax.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdSalesTax.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adpSalesTax As New SqlDataAdapter(cmdSalesTax)
            Dim dtSalesTax As New DataTable("SalesTax")
            adpSalesTax.Fill(dtSalesTax)
            ds.Tables.Add(dtSalesTax)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadSalesmanCommissionData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Salesman.SalesmanName, InvoiceInfo.InvoiceNo, InvoiceInfo.InvoiceDate, InvoiceInfo.GrandTotal, (InvoiceInfo.GrandTotal * Salesman.Commission / 100) AS CommissionAmount FROM InvoiceInfo INNER JOIN Salesman ON InvoiceInfo.SalesmanID = Salesman.SalesmanID WHERE InvoiceInfo.InvoiceDate BETWEEN @dateFrom AND @dateTo ORDER BY Salesman.SalesmanName, InvoiceInfo.InvoiceDate", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("SalesmanCommission")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadCustomerLedgerData(customerID As String, dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit FROM LedgerBook WHERE PartyID = @customerID AND Date BETWEEN @dateFrom AND @dateTo ORDER BY Date", con)
            cmd.Parameters.AddWithValue("@customerID", customerID)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("CustomerLedger")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            ' تحميل بيانات العميل
            Dim cmdCustomer As New SqlCommand("SELECT * FROM Customer WHERE CustomerID = @customerID", con)
            cmdCustomer.Parameters.AddWithValue("@customerID", customerID)
            Dim adpCustomer As New SqlDataAdapter(cmdCustomer)
            Dim dtCustomer As New DataTable("Customer")
            adpCustomer.Fill(dtCustomer)
            ds.Tables.Add(dtCustomer)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadSupplierLedgerData(supplierID As String, dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit FROM SupplierLedgerBook WHERE PartyID = @supplierID AND Date BETWEEN @dateFrom AND @dateTo ORDER BY Date", con)
            cmd.Parameters.AddWithValue("@supplierID", supplierID)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("SupplierLedger")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            ' تحميل بيانات المورد
            Dim cmdSupplier As New SqlCommand("SELECT * FROM Supplier WHERE SupplierID = @supplierID", con)
            cmdSupplier.Parameters.AddWithValue("@supplierID", supplierID)
            Dim adpSupplier As New SqlDataAdapter(cmdSupplier)
            Dim dtSupplier As New DataTable("Supplier")
            adpSupplier.Fill(dtSupplier)
            ds.Tables.Add(dtSupplier)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadSupplierPurchaseData(supplierName As String, dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            ' تحميل بيانات المشتريات حسب المورد
            Dim cmdPurchase As New SqlCommand("SELECT DISTINCT Stock.ST_ID, Stock.InvoiceNo, Stock.Date, Stock.SupplierID, Stock.GrandTotal, Stock.TotalPayment, Stock.PaymentDue, Stock.Remarks, Stock_Product.SP_ID, Stock_Product.StockID, Stock_Product.ProductID, Stock_Product.Qty, Stock_Product.Price, Stock_Product.TotalAmount, Supplier.ID, Supplier.SupplierID AS Expr1, Supplier.Name, Supplier.Address, Supplier.City, Supplier.State, Supplier.ZipCode, Supplier.ContactNo, Supplier.EmailID, Supplier.Remarks AS Expr2, Product.PID, Product.ProductCode, Product.ProductName, Product.SubCategoryID, Product.Description, Product.CostPrice, Product.SellingPrice, Product.Discount, Product.VAT, Product.ReorderPoint FROM Stock INNER JOIN Stock_Product ON Stock.ST_ID = Stock_Product.StockID INNER JOIN Supplier ON Stock.SupplierID = Supplier.ID INNER JOIN Product ON Stock_Product.ProductID = Product.PID WHERE Supplier.Name = @supplierName AND Stock.Date BETWEEN @dateFrom AND @dateTo ORDER BY Stock.Date", con)
            cmdPurchase.Parameters.AddWithValue("@supplierName", supplierName)
            cmdPurchase.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdPurchase.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adpPurchase As New SqlDataAdapter(cmdPurchase)
            Dim dtPurchase As New DataTable("SupplierPurchase")
            adpPurchase.Fill(dtPurchase)
            ds.Tables.Add(dtPurchase)

            ' تحميل إجماليات المشتريات
            Dim cmdTotals As New SqlCommand("SELECT ISNULL(SUM(GrandTotal),0) AS TotalAmount, ISNULL(SUM(TotalPayment),0) AS TotalPaid, ISNULL(SUM(PaymentDue),0) AS TotalDue FROM Stock INNER JOIN Supplier ON Stock.SupplierID = Supplier.ID WHERE Supplier.Name = @supplierName AND Stock.Date BETWEEN @dateFrom AND @dateTo", con)
            cmdTotals.Parameters.AddWithValue("@supplierName", supplierName)
            cmdTotals.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmdTotals.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adpTotals As New SqlDataAdapter(cmdTotals)
            Dim dtTotals As New DataTable("PurchaseTotals")
            adpTotals.Fill(dtTotals)
            ds.Tables.Add(dtTotals)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadStockInData(ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT RTRIM(Product.ProductCode) AS ProductCode, RTRIM(ProductName) AS ProductName, SellingPrice, Discount, VAT, Qty FROM Temp_Stock INNER JOIN Product ON Product.PID = Temp_Stock.ProductID WHERE Qty > 0 ORDER BY ProductName", con)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("StockIn")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadStockOutData(ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT RTRIM(Product.ProductCode) AS ProductCode, RTRIM(ProductName) AS ProductName, SellingPrice, Discount, VAT FROM Temp_Stock INNER JOIN Product ON Product.PID = Temp_Stock.ProductID WHERE Qty <= 0 ORDER BY ProductName", con)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("StockOut")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadSalesTaxData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT InvoiceNo, InvoiceDate, Customer.CustomerID, Name, SUM(VAT) AS TotalVAT FROM InvoiceInfo INNER JOIN Customer ON InvoiceInfo.CustomerID = Customer.ID INNER JOIN Invoice_Product ON InvoiceInfo.Inv_ID = Invoice_Product.InvoiceID WHERE InvoiceDate BETWEEN @dateFrom AND @dateTo GROUP BY InvoiceNo, InvoiceDate, Customer.CustomerID, Name HAVING SUM(VAT) > 0 ORDER BY Name", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("SalesTax")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadServiceTaxData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT InvoiceNo, InvoiceDate, Customer.CustomerID, Name, SUM(VAT) AS TotalVAT, ServiceTax FROM InvoiceInfo1 INNER JOIN Invoice1_Product ON InvoiceInfo1.Inv_ID = Invoice1_Product.InvoiceID INNER JOIN Service ON InvoiceInfo1.ServiceID = Service.S_ID INNER JOIN Customer ON Service.CustomerID = Customer.ID WHERE InvoiceDate BETWEEN @dateFrom AND @dateTo GROUP BY InvoiceNo, InvoiceDate, Customer.CustomerID, Name, ServiceTax HAVING (SUM(VAT) + ServiceTax) > 0 ORDER BY Name", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("ServiceTax")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub



    Private Shared Sub LoadGeneralDayBookData(reportDate As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            Dim cmd As New SqlCommand("SELECT Date, Name, LedgerNo, Label, Debit, Credit FROM LedgerBook WHERE Date BETWEEN @dateFrom AND @dateTo ORDER BY LedgerNo", con)
            cmd.Parameters.AddWithValue("@dateFrom", reportDate.Date)
            cmd.Parameters.AddWithValue("@dateTo", reportDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59))

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("GeneralDayBook")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadProfitAndLossData(dateFrom As DateTime, dateTo As DateTime, ds As DataSet)
        Try
            con = New SqlConnection(cs)
            con.Open()

            ' تحميل بيانات الإيرادات والمصروفات
            Dim cmd As New SqlCommand("SELECT 'Revenue' AS Type, SUM(GrandTotal) AS Amount FROM InvoiceInfo WHERE InvoiceDate BETWEEN @dateFrom AND @dateTo UNION ALL SELECT 'Expense' AS Type, SUM(GrandTotal) AS Amount FROM PurchaseInfo WHERE PurchaseDate BETWEEN @dateFrom AND @dateTo", con)
            cmd.Parameters.AddWithValue("@dateFrom", dateFrom)
            cmd.Parameters.AddWithValue("@dateTo", dateTo)

            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("ProfitAndLoss")
            adp.Fill(dt)
            ds.Tables.Add(dt)

            LoadCompanyDataStatic(ds)
            con.Close()
        Catch ex As Exception
            If con.State = ConnectionState.Open Then con.Close()
            Throw ex
        End Try
    End Sub

    Private Shared Sub LoadCompanyDataStatic(ds As DataSet)
        Try
            If con.State = ConnectionState.Closed Then
                con = New SqlConnection(cs)
                con.Open()
            End If

            Dim cmd As New SqlCommand("SELECT * FROM Company", con)
            Dim adp As New SqlDataAdapter(cmd)
            Dim dt As New DataTable("Company")
            adp.Fill(dt)

            If Not ds.Tables.Contains("Company") Then
                ds.Tables.Add(dt)
            End If

        Catch ex As Exception
            Throw ex
        End Try
    End Sub
End Class
