Imports System.Data.SqlClient
Imports System.Windows.Forms

Namespace Sales_and_Inventory_System
    Public Class ReportManager
        Private Shared cs As String = "Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\Database.mdf;Integrated Security=True;User Instance=True"
        Private Shared con As SqlConnection

        ' دالة لعرض تقرير الفاتورة
        Public Shared Sub ShowInvoiceReport(invoiceNo As String, customerType As String)
            Try
                MessageBox.Show($"تقرير الفاتورة رقم: {invoiceNo}" & vbCrLf & $"نوع العميل: {customerType}", "تقرير الفاتورة", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير الفاتورة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير المبيعات
        Public Shared Sub ShowSalesReport(dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"تقرير المبيعات من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "تقرير المبيعات", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير المبيعات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير المخزون
        Public Shared Sub ShowStockInReport(dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"تقرير المخزون من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "تقرير المخزون", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير المخزون: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير العملاء
        Public Shared Sub ShowCustomerReport()
            Try
                MessageBox.Show("تقرير العملاء", "تقرير العملاء", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير العملاء: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير الموردين
        Public Shared Sub ShowSupplierReport()
            Try
                MessageBox.Show("تقرير الموردين", "تقرير الموردين", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير الموردين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير كشف حساب العميل
        Public Shared Sub ShowCustomerLedgerReport(customerId As String, dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"كشف حساب العميل: {customerId}" & vbCrLf & $"من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "كشف حساب العميل", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض كشف حساب العميل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير كشف حساب المورد
        Public Shared Sub ShowSupplierLedgerReport(supplierId As String, dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"كشف حساب المورد: {supplierId}" & vbCrLf & $"من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "كشف حساب المورد", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض كشف حساب المورد: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير ضريبة المبيعات
        Public Shared Sub ShowSalesTaxReport(dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"تقرير ضريبة المبيعات من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "تقرير ضريبة المبيعات", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير ضريبة المبيعات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير عمولة المندوب
        Public Shared Sub ShowSalesmanCommissionReport(salesmanId As String, dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"تقرير عمولة المندوب: {salesmanId}" & vbCrLf & $"من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "تقرير عمولة المندوب", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير عمولة المندوب: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير دفتر الأستاذ العام
        Public Shared Sub ShowGeneralLedgerReport(dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"تقرير دفتر الأستاذ العام من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "تقرير دفتر الأستاذ العام", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير دفتر الأستاذ العام: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير دفتر اليومية
        Public Shared Sub ShowGeneralDayBookReport(reportDate As DateTime)
            Try
                MessageBox.Show($"تقرير دفتر اليومية لتاريخ: {reportDate.ToString("dd/MM/yyyy")}", "تقرير دفتر اليومية", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير دفتر اليومية: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير الأرباح والخسائر
        Public Shared Sub ShowProfitAndLossReport(dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"تقرير الأرباح والخسائر من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "تقرير الأرباح والخسائر", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير الأرباح والخسائر: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير المدينين
        Public Shared Sub ShowDebtorsReport(dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"تقرير المدينين من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "تقرير المدينين", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير المدينين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ' دالة لعرض تقرير المشتريات
        Public Shared Sub ShowPurchaseReport(dateFrom As DateTime, dateTo As DateTime)
            Try
                MessageBox.Show($"تقرير المشتريات من: {dateFrom.ToString("dd/MM/yyyy")}" & vbCrLf & $"إلى: {dateTo.ToString("dd/MM/yyyy")}", "تقرير المشتريات", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show("خطأ في عرض تقرير المشتريات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

    End Class
End Namespace
