<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="InvoiceDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="InvoiceInfo">
      <Query>
        <DataSourceName>InvoiceDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="InvoiceNo">
          <DataField>InvoiceNo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="InvoiceDate">
          <DataField>InvoiceDate</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="CustomerName">
          <DataField>Name</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="GrandTotal">
          <DataField>GrandTotal</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TotalPaid">
          <DataField>TotalPaid</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Balance">
          <DataField>Balance</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="InvoiceProduct">
      <Query>
        <DataSourceName>InvoiceDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="ProductName">
          <DataField>ProductName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Qty">
          <DataField>Qty</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="SellingPrice">
          <DataField>SellingPrice</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Amount">
          <DataField>Amount</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TotalAmount">
          <DataField>TotalAmount</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Company">
      <Query>
        <DataSourceName>InvoiceDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CompanyName">
          <DataField>CompanyName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Address">
          <DataField>Address</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ContactNo">
          <DataField>ContactNo</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Textbox Name="CompanyName">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>=First(Fields!CompanyName.Value, "Company")</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>16pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>CompanyName</rd:DefaultName>
            <Top>0.25in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>7in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="InvoiceTitle">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>فاتورة مبيعات</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>14pt</FontSize>
                      <FontWeight>Bold</FontWeight>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>InvoiceTitle</rd:DefaultName>
            <Top>0.75in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>7in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="InvoiceNo">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="رقم الفاتورة: " + First(Fields!InvoiceNo.Value, "InvoiceInfo")</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>12pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>InvoiceNo</rd:DefaultName>
            <Top>1.25in</Top>
            <Left>0.5in</Left>
            <Height>0.25in</Height>
            <Width>3in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Textbox Name="InvoiceDate">
            <CanGrow>true</CanGrow>
            <KeepTogether>true</KeepTogether>
            <Paragraphs>
              <Paragraph>
                <TextRuns>
                  <TextRun>
                    <Value>="التاريخ: " + Format(First(Fields!InvoiceDate.Value, "InvoiceInfo"), "dd/MM/yyyy")</Value>
                    <Style>
                      <FontFamily>Arial</FontFamily>
                      <FontSize>12pt</FontSize>
                    </Style>
                  </TextRun>
                </TextRuns>
                <Style />
              </Paragraph>
            </Paragraphs>
            <rd:DefaultName>InvoiceDate</rd:DefaultName>
            <Top>1.25in</Top>
            <Left>4.5in</Left>
            <Height>0.25in</Height>
            <Width>3in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
              <PaddingLeft>2pt</PaddingLeft>
              <PaddingRight>2pt</PaddingRight>
              <PaddingTop>2pt</PaddingTop>
              <PaddingBottom>2pt</PaddingBottom>
            </Style>
          </Textbox>
          <Tablix Name="ProductTable">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.5in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ProductNameHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>اسم المنتج</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ProductNameHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>LightGrey</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>InvoiceProduct</DataSetName>
            <Top>2in</Top>
            <Left>0.5in</Left>
            <Height>0.5in</Height>
            <Width>7.5in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>4in</Height>
        <Style />
      </Body>
      <Width>8.5in</Width>
      <Page>
        <PageHeight>11in</PageHeight>
        <PageWidth>8.5in</PageWidth>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <ColumnSpacing>0.13cm</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="InvoiceNo">
      <DataType>String</DataType>
      <Prompt>Invoice Number</Prompt>
    </ReportParameter>
    <ReportParameter Name="PrintDate">
      <DataType>String</DataType>
      <Prompt>Print Date</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</rd:ReportID>
</Report>
