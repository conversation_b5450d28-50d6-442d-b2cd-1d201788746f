﻿Imports System.Data.SqlClient
Imports Microsoft.Win32

Public Class frmSplash
    Private Const trialDays As Integer = 30 ' عدد أيام الفترة التجريبية
    Private registryPath As String = "Software\MyApp" ' مسار حفظ البيانات في الريجستري

    Private Sub Timer1_Tick(sender As System.Object, e As System.EventArgs) Handles Timer1.Tick
        Try
            ' التحقق من تاريخ التثبيت
            Dim installDate As Date = GetInstallDate()
            Dim currentDate As Date = DateTime.Now

            ' حساب الأيام المتبقية
            Dim daysUsed As Integer = (currentDate - installDate).Days
            Dim daysLeft As Integer = trialDays - daysUsed

            ' التحقق من وجود تفعيل أولاً
            If IsActivated() Then
                ' البرنامج مفعل، متابعة التحميل
                ProgressBar1.Visible = True
                ProgressBar1.Value += 2

                Select Case ProgressBar1.Value
                    Case 10 : lblSet.Text = "Reading modules.."
                    Case 20 : lblSet.Text = "Turning on modules."
                    Case 40 : lblSet.Text = "Starting modules.."
                    Case 60 : lblSet.Text = "Loading modules.."
                    Case 80 : lblSet.Text = "Done Loading modules.."
                    Case 100
                        frmLogin.Show()
                        Timer1.Enabled = False
                        Me.Hide()
                End Select
            ElseIf daysLeft <= 0 Then
                ' انتهت الفترة التجريبية ولا يوجد تفعيل، الانتقال إلى شاشة التفعيل
                frmActivation.Show()
                Me.Hide()
            Else
                ' الفترة التجريبية لم تنته، متابعة التحميل
                ProgressBar1.Visible = True
                ProgressBar1.Value += 2

                Select Case ProgressBar1.Value
                    Case 10 : lblSet.Text = "Reading modules.. (Trial: " & daysLeft & " days left)"
                    Case 20 : lblSet.Text = "Turning on modules."
                    Case 40 : lblSet.Text = "Starting modules.."
                    Case 60 : lblSet.Text = "Loading modules.."
                    Case 80 : lblSet.Text = "Done Loading modules.."
                    Case 100
                        frmLogin.Show()
                        Timer1.Enabled = False
                        Me.Hide()
                End Select
            End If
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "Error!")
            End
        End Try
    End Sub

    Private Sub frmSplash_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Try
            ' ضبط تاريخ التثبيت عند أول تشغيل
            Dim installDate As Date = GetInstallDate()
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical, "Error!")
            End
        End Try
    End Sub

    ' **دالة استرجاع أو حفظ تاريخ التثبيت**
    Private Function GetInstallDate() As Date
        Try
            Dim regKey As RegistryKey = Registry.CurrentUser.OpenSubKey(registryPath, True)
            If regKey Is Nothing Then
                ' أول مرة تشغيل، نحفظ التاريخ الحالي
                regKey = Registry.CurrentUser.CreateSubKey(registryPath)
                regKey.SetValue("InstallDate", DateTime.Now.ToString("yyyy-MM-dd"))
                regKey.Close()
                Return DateTime.Now
            Else
                ' استرجاع التاريخ المحفوظ
                Dim savedDate As String = regKey.GetValue("InstallDate", "").ToString()
                regKey.Close()
                Return Date.ParseExact(savedDate, "yyyy-MM-dd", Nothing)
            End If
        Catch ex As Exception
            Return DateTime.Now ' في حالة حدوث خطأ، نعتبر أن التثبيت حدث الآن
        End Try
    End Function

    ' **دالة فحص التفعيل**
    Private Function IsActivated() As Boolean
        Try
            Dim con As New SqlConnection(cs)
            con.Open()
            Dim cmd As New SqlCommand("SELECT COUNT(*) FROM Activation", con)
            Dim count As Integer = Convert.ToInt32(cmd.ExecuteScalar())
            con.Close()
            Return count > 0
        Catch ex As Exception
            ' في حالة حدوث خطأ (مثل عدم وجود جدول التفعيل)، نعتبر أن البرنامج غير مفعل
            Return False
        End Try
    End Function

    Private Sub PictureBox1_Click(sender As Object, e As EventArgs) Handles PictureBox1.Click
        ' نقرة على الصورة لفتح نافذة التفعيل للاختبار
        Timer1.Enabled = False
        frmActivation.Show()
        Me.Hide()
    End Sub
End Class
